"""Platform detector for IBEW job sites."""
from typing import Dict, Any
import re
from urllib.parse import urlparse
import aiohttp
import logging
import json
import asyncio
from pathlib import Path
from datetime import datetime

# Set up logging
log_dir = Path(r"X:\Journeyman_Jobs\v3\output\logs")
log_dir.mkdir(parents=True, exist_ok=True)
log_file = log_dir / f"platform_detection_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class PlatformDetector:
    """Detects the platform/CMS of IBEW job sites."""
    
    PATTERNS = {
        'working_systems': {
            'url': r'workingsystems\.com',
            'content': ['knockout.js', 'OpenJob', 'ko.observableArray']
        },
        'union_active': {
            'url': r'unionactive\.com|/unionactive/',
            'content': ['UnionActive', 'zone=/unionactive']
        },
        'wordpress': {
            'url': r'wp-content|wp-includes',
            'content': ['wp-content', 'wp-includes', 'wordpress']
        }
    }
    
    async def detect(self, url: str) -> Dict[str, Any]:
        """Detect platform from URL and initial page content."""
        # First check URL patterns
        platform_scores = {p: 0.0 for p in self.PATTERNS.keys()}
        
        # Check URL patterns
        for platform, patterns in self.PATTERNS.items():
            if re.search(patterns['url'], url, re.I):
                platform_scores[platform] += 0.4
        
        # Check page content
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    content = await response.text()
                    
                    for platform, patterns in self.PATTERNS.items():
                        content_matches = sum(1 for p in patterns['content'] 
                                           if p.lower() in content.lower())
                        platform_scores[platform] += 0.2 * (content_matches / len(patterns['content']))
        except Exception as e:
            logger.warning(f"Error fetching {url}: {str(e)}")
        
        # Get platform with highest score
        best_platform = max(platform_scores.items(), key=lambda x: x[1])
        
        return {
            'platform': best_platform[0],
            'confidence': best_platform[1],
            'metadata': {
                'url': url,
                'scores': platform_scores
            }
        }

async def process_urls():
    # Read the JSON file with encoding handling
    json_path = Path(r"X:\Journeyman_Jobs\v3\crawl4ai\data\outside_dispatch.json")
    
    try:
        # First try UTF-8
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except UnicodeDecodeError:
        try:
            # If UTF-8 fails, try with utf-8-sig (handles BOM)
            with open(json_path, 'r', encoding='utf-8-sig') as f:
                data = json.load(f)
        except UnicodeDecodeError:
            try:
                # Last resort: try Windows-1252 encoding
                with open(json_path, 'r', encoding='cp1252') as f:
                    data = json.load(f)
            except Exception as e:
                logging.error(f"Failed to read JSON file with multiple encodings: {e}")
                return
    except Exception as e:
        logging.error(f"Failed to read JSON file: {e}")
        return

    detector = PlatformDetector()
    results = []

    # Process each object
    for item in data:
        # Get all URLs from the object
        urls = []
        if isinstance(item, dict):
            urls.extend([
                val for key, val in item.items() 
                if isinstance(val, str) and 
                (key.lower().endswith('url') or key.lower().endswith('link'))
            ])

        if not urls:
            logging.warning(f"No URLs found in object: {item}")
            continue

        # Try each URL until we get a good result
        best_result = None
        for url in urls:
            try:
                result = await detector.detect(url)
                if result['confidence'] > 0.4:  # Adjust threshold as needed
                    best_result = {**result, 'original_data': item}
                    break
            except Exception as e:
                logging.warning(f"Error processing URL {url}: {e}")
                continue

        if best_result:
            results.append(best_result)
        else:
            logging.warning(f"Could not detect platform for any URLs in: {item}")

    # Save results
    results_file = log_dir / f"detection_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    try:
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2)
        logging.info(f"Results saved to {results_file}")
    except Exception as e:
        logging.error(f"Failed to save results: {e}")

if __name__ == "__main__":
    asyncio.run(process_urls())
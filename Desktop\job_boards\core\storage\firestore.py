# x:\Journeyman_Jobs\v3\core\storage\firestore.py

import firebase_admin
from firebase_admin import credentials, firestore
import json

# Initialize Firebase Admin SDK
cred = credentials.Certificate("x:\\Journeyman_Jobs\\v3\\core\\storage\\config\\firebase_config.json")
firebase_admin.initialize_app(cred)

# Get a reference to the Firestore database
db = firestore.client()

# Load data from master.json
with open('x:\\Journeyman_Jobs\\v3\\data\\master.json', 'r') as f:
    data = json.load(f)

# Sort the data based on local_union number (converting to integer for proper numerical sorting)
sorted_data = sorted(data, key=lambda x: int(x['local_union']))

# Upload data to Firestore
for doc in sorted_data:
    # Use the local_union number as the document ID
    local_number = doc['local_union']
    db.collection('locals').document(local_number).set(doc)

print("Data uploaded to Firestore successfully!")
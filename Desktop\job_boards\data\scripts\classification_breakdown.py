import re
import json
from collections import defaultdict


classifications = {
    'Atomic Research Services': 'ars',
    'Alarm And Signal': 'as',
    'Bridge Operators': 'bo',
    'Communications': 'c',
    'Cable Television': 'catv',
    'Cranemen': 'cr',
    'Cable Spicers': 'cs',
    'Electrical Equipment Service': 'ees',
    'Electrical Inspection': 'ei',
    'Electrical Manufacturing': 'em',
    'Electric Signs': 'es',
    'Electronic Technicians': 'et',
    'Fixture Manufacturing': 'fm',
    'Government': 'govt',
    'Inside': 'i',
    'Instrument Technicians': 'it',
    'Line Clearance Tree Trimming': 'lctt',
    'Lightning Protection Technicians': 'lpt',
    'Marine': 'mar',
    'Maintenance And Operation': 'mo',
    'Manufacturing Office Workers': 'mow',
    'Motion Picture Studios': 'mps',
    'Maintenance': 'mt',
    'Nuclear Service Technicians': 'nst',
    'Outside': 'o',
    'Powerhouse': 'p',
    'Professional Engineers And Technicians': 'pet',
    'Professional Technical And Clerical': 'ptc',
    'Railroad': 'rr',
    'Radio-Television Broadcasting': 'rtb',
    'Radio-Television Manufacturing': 'rtm',
    'Radio-Television Service': 'rts',
    'Shopmen': 's',
    'Sign Erectors': 'se',
    'Service Operation': 'so',
    'Sound And Public Address': 'spa',
    'Sound Technicians': 'st',
    'Telephone': 't',
    'Transportation Manufacturing': 'tm',
    'Utility': 'u',
    'Utility Office Workers': 'uow',
    'Warehouse And Supply': 'ws'
}

def load_master_json(file_path):
    with open(file_path, 'r') as f:
        return json.load(f)

def parse_classifications(classification_str):
    """Parse both parenthesized and comma-separated classification codes."""
    matches = []
    # Check for parenthesized codes: (i), (o), etc.
    parentheses_pattern = r'\(([^)]+)\)'
    parentheses_matches = re.findall(parentheses_pattern, classification_str)
    
    # Check for comma-separated codes
    if ',' in classification_str:
        comma_matches = [code.strip() for code in classification_str.split(',')]
        matches.extend(comma_matches)
    
    matches.extend(parentheses_matches)
    return matches

def process_data(data):
    state_data = defaultdict(lambda: defaultdict(list))
    classification_data = defaultdict(lambda: defaultdict(list))
    all_locals = set()

    for item in data:
        local_union = item.get('local_union', '')
        state = item.get('state', '').upper()
        classification_str = item.get('classification', '')
        
        # Handle Canadian locals
        if state in ['AB', 'BC', 'MB', 'NB', 'NL', 'NS', 'ON', 'PE', 'QC', 'SK', 'YT', 'NT', 'NU']:
            state = 'CANADA'
            
        all_locals.add(local_union)
        state_data[state]['locals'].append(local_union)
        
        # Process classifications
        codes = parse_classifications(classification_str)
        for classification_name, code in classifications.items():
            pattern = rf'(?:\({code}\)|(?:^|,)\s*{code}\s*(?:,|$))'
            if any(re.search(pattern, c, re.IGNORECASE) for c in codes):
                classification_data[classification_name][state].append(local_union)

    return {
        'total_locals': len(all_locals),
        'all_locals': sorted(list(all_locals)),
        'state_data': dict(state_data),
        'classification_data': dict(classification_data)
    }

def write_markdown_report(data, output_file):
    with open(output_file, 'w') as f:
        # Write header and total locals
        f.write("# IBEW Local Union Classification Breakdown\n\n")
        f.write(f"## Total Local Unions: {data['total_locals']}\n\n")
        
        # Write all locals
        f.write("## All Local Unions\n")
        for local in data['all_locals']:
            f.write(f"- {local}\n")
        f.write("\n")
        
        # Write state breakdown
        f.write("## Breakdown by State/Region\n\n")
        for state, info in data['state_data'].items():
            locals_list = sorted(set(info['locals']))
            f.write(f"### {state}\n")
            f.write(f"Total Locals: {len(locals_list)}\n\n")
            for local in locals_list:
                f.write(f"- {local}\n")
            f.write("\n")
        
        # Write classification breakdown
        f.write("## Breakdown by Classification\n\n")
        for classification, states in data['classification_data'].items():
            f.write(f"### {classification}\n")
            total_in_classification = sum(len(set(locals)) for locals in states.values())
            f.write(f"Total Locals: {total_in_classification}\n\n")
            
            for state, locals_list in states.items():
                unique_locals = sorted(set(locals_list))
                if unique_locals:
                    f.write(f"#### {state}\n")
                    f.write(f"Total: {len(unique_locals)}\n")
                    for local in unique_locals:
                        f.write(f"- {local}\n")
                    f.write("\n")
            f.write("\n")

def main():
    # Load data from your master.json file
    data = load_master_json('data/master.json')
    
    # Process the data
    processed_data = process_data(data)
    
    # Write the report
    write_markdown_report(processed_data, 'data/classification_breakdown.md')

if __name__ == "__main__":
    main()
import json
from collections import defaultdict

# Input file path
input_file = 'v3/data/crawled/platform.json'

# Output file path
output_file = 'v3/data/outputs/platform_output.json'

# Read the input JSON file
with open(input_file, 'r') as f:
    data = json.load(f)

# Group local_union values by platform
platform_dict = defaultdict(list)
for entry in data:
    local_union = entry['local_union']
    platform = entry['platform']
    platform_dict[platform].append(local_union)

# Convert defaultdict to regular dict for JSON serialization
platform_dict = dict(platform_dict)

# Save the output JSON file
with open(output_file, 'w') as f:
    json.dump(platform_dict, f, indent=4)

print(f"Output saved to {output_file}")
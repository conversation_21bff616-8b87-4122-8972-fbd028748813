const puppeteer = require('puppeteer-core');
const path = require('path');
const { Firestore, FieldValue } = require('@google-cloud/firestore');

// Set the environment variable for Google Cloud credentials
process.env.GOOGLE_APPLICATION_CREDENTIALS = path.resolve(__dirname, 'X:/Journeyman_Jobs/v3/core/storage/config/firebase_config.json');

// Proxy Configuration (if needed)
const SBR_WS_ENDPOINT = `wss://brd-customer-hl_482339a5-zone-scraping222_111:<EMAIL>:9222`;

// Firebase Configuration
const firebaseConfig = {
  projectId: 'journeyman-jobs',
  keyFilename: path.resolve(__dirname, 'X:/Journeyman_Jobs/v3/core/storage/config/firebase_config.json')
};

const db = new Firestore(firebaseConfig);

// Function to sanitize strings for Firestore document IDs
function sanitizeString(str) {
  return str.replace(/[\/\\\.\#\$\[\]]/g, '').replace(/\s+/g, '_');
}

// Function to generate a consistent job ID
function generateJobId(localNumber, position, company) {
  return `${localNumber}-${sanitizeString(position)}-${sanitizeString(company)}`;
}

async function main() {
    console.log('Starting script...');
    const browser = await puppeteer.connect({
        browserWSEndpoint: SBR_WS_ENDPOINT,
    });

    try {
        console.log('Connected! Navigating...');
        const page = await browser.newPage();

        const url = 'https://www.ibew245.com/index.cfm?zone=/unionactive/view_page.cfm&page=Job20Board';
        await page.goto(url, { waitUntil: 'networkidle2' });

        console.log('Navigated! Waiting for job listings to load...');
        await page.waitForSelector('tbody', { timeout: 120000 });

        // Extract job listing information with better filtering
        const jobs = await page.evaluate(() => {
            const jobElements = Array.from(document.querySelectorAll('tbody > tr'));
            const jobList = [];
            
            // Skip header rows and empty rows
            jobElements.forEach(row => {
                const cells = row.querySelectorAll('td');
                if (cells.length === 8) {  // Only process rows with exactly 8 cells
                    const contractor = cells[0]?.innerText.trim();
                    // Only add if it's a real job listing (checking for actual contractor name)
                    if (contractor && contractor !== 'CONTRACTOR' && !contractor.includes('IF YOU ARE INTERESTED')) {
                        const jobData = {
                            contractor: contractor,
                            location: cells[1]?.innerText.trim(),
                            jobDescription: cells[2]?.innerText.trim(),
                            startDate: cells[3]?.innerText.trim(),
                            requirements: cells[4]?.innerText.trim(),
                            positions: cells[5]?.innerText.trim(),
                            hours: cells[6]?.innerText.trim(),
                            perDiem: cells[7]?.innerText.trim(),
                        };
                        // Additional validation - only add if we have actual data
                        if (jobData.location !== 'Unknown Location' && 
                            jobData.jobDescription !== 'No Description Available') {
                            jobList.push(jobData);
                        }
                    }
                }
            });
            return jobList;
        });

        console.log('Scraped job listings:', jobs);
        // Optional: Save to file
        const localNumber = '245';

        // Fetch existing jobs from Firestore for the local number
        const firestoreJobsSnapshot = await db.collection('jobs').where('localNumber', '==', localNumber).get();
        const firestoreJobsMap = new Map();
        firestoreJobsSnapshot.forEach(doc => {
          firestoreJobsMap.set(doc.id, doc.data());
        });
    
        // Prepare a set to keep track of current job IDs
        const currentJobIds = new Set();
    
        // Process combined jobs
        for (const job of combinedJobs) {
          const jobId = generateJobId(localNumber, job.jobClass, job.employer);
          currentJobIds.add(jobId);
    
          const jobData = {
            ...job,
            localNumber,
            timestamp: firestoreJobsMap.has(jobId) ? firestoreJobsMap.get(jobId).timestamp : FieldValue.serverTimestamp()
          };
    
          const jobDocRef = db.collection('jobs').doc(jobId);
    
          if (firestoreJobsMap.has(jobId)) {
            // Update existing job without changing the initial timestamp
            await jobDocRef.update({
              ...jobData,
              timestamp: firestoreJobsMap.get(jobId).timestamp
            });
            console.log(`Updated job ${jobId}`);
          } else {
            // Create new job with initial timestamp
            await jobDocRef.set(jobData);
            console.log(`Added new job ${jobId}`);
          }
        }
    
        // Delete jobs from Firestore that are no longer on the website
        for (const [jobId, jobData] of firestoreJobsMap.entries()) {
          if (!currentJobIds.has(jobId)) {
            await db.collection('jobs').doc(jobId).delete();
            console.log(`Deleted outdated job ${jobId}`);
          }
        }
    
      } catch (err) {
        console.error('An error occurred:', err);
      }
    }
    
    if (require.main === module) {
      main().catch(err => {
        console.error(err.stack || err);
        process.exit(1);
      });
    }
    
    module.exports = main;
    

"""Smart crawler for IBEW local data collection"""

"""Smart crawler for IBEW local data collection"""
import os
from dotenv import load_dotenv
import json
from typing import Dict, List, Optional, Set
from dataclasses import dataclass
import asyncio
from playwright.async_api import async_playwright, Browser, Page
import logging
from pathlib import Path
from datetime import datetime
import re
from urllib.parse import urljoin, urlparse

# Load .env file
load_dotenv()

# Setup paths
BASE_DIR = Path('X:/Journeyman_Jobs/v3')
LOGS_DIR = BASE_DIR / 'logs'
DATA_DIR = BASE_DIR / 'data'
CRAWL_DIR = DATA_DIR / 'crawled'

# Create directories if they don't exist
LOGS_DIR.mkdir(parents=True, exist_ok=True)
CRAWL_DIR.mkdir(parents=True, exist_ok=True)

# Set up logging to the correct directory
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOGS_DIR / 'crawler.log'),
        logging.StreamHandler()
    ]
)

# Get Bright Data auth from env
BRIGHT_DATA_AUTH = os.getenv('BRIGHT_DATA_AUTH')
if not BRIGHT_DATA_AUTH:
    raise ValueError("BRIGHT_DATA_AUTH not found in .env file")

@dataclass
class LocalData:
    local_number: str
    base_url: str
    job_board_url: Optional[str] = None
    initial_sign: Optional[str] = None
    resign_procedure: Optional[str] = None
    resign_url: Optional[str] = None
    resign_email: Optional[str] = None
    resign_fax: Optional[str] = None
    referral: Optional[str] = None

@dataclass
class CrawlResult:
    url: str
    html_content: str
    linked_pages: List[str]
    status: str
    error: Optional[str] = None
    metadata: Dict = None

class SmartCrawler:
    def __init__(
        self,
        bright_data_auth: str,
        batch_size: int = 20,
        max_concurrent: int = 2,  # Reduced from 3 to 2
        requests_per_second: int = 1  # Reduced from 2 to 1
    ):
        self.bright_data_url = f'https://{bright_data_auth}@brd.superproxy.io:9222'
        self.batch_size = batch_size
        self.logger = logging.getLogger(__name__)
        
        # Rate limiting
        self.max_concurrent = max_concurrent
        self.delay = 1.0 / requests_per_second
        self.semaphore = asyncio.Semaphore(max_concurrent)
        
        # Track crawled URLs
        self.crawled_urls: Set[str] = set()
        
        # Combined patterns from your files
        self.patterns = {
            'job_board': [
                r'/jobs?/?',
                r'/dispatch/?',
                r'/referr?al/?',
                r'/work-?list/?',
                r'/job-?board/?',
                r'/open-?calls/?',
                r'/job-?hotline/?',
                r'dispatch-?list',
                r'workeropenjobs',
                r'job_calls',
                r'Available.*Jobs'
            ],
            'resign': [
                r'/resign/?',
                r'/re-?sign/?',
                r'/book-?sign/?',
                r'resign_form',
                r'resigns?\.cfm',
                r'sign.*books?',
                r'book.*sign'
            ],
            'referral': [
                r'/referr?al-?system/?',
                r'/referr?al-?procedure/?',
                r'/dispatch-?procedure/?',
                r'rules.*road',
                r'referral.*guidelines?'
            ]
        }

    async def init_browser(self) -> Browser:
        playwright = await async_playwright().start()
        browser = await playwright.chromium.connect_over_cdp(
            self.bright_data_url,
            timeout=60000,  # 60 second timeout
            ignore_https_errors=True  # Ignore SSL errors
        )
        return browser

    def should_follow_link(self, url: str, base_url: str) -> bool:
        if not url.startswith(base_url):
            return False
            
        skip_patterns = [
            r'/contact',
            r'/about',
            r'/news',
            r'/events',
            r'/login',
            r'/signin',
            r'/photos?',
            r'/gallery',
            r'/calendar'
        ]
        
        if any(re.search(pattern, url, re.I) for pattern in skip_patterns):
            return False
            
        for pattern_list in self.patterns.values():
            if any(re.search(pattern, url, re.I) for pattern in pattern_list):
                return True
                
        return False

    async def extract_contact_info(self, content: str) -> Dict[str, Optional[str]]:
        """Extract contact information from page content"""
        contact_info = {
            'email': None,
            'fax': None
        }
        
        # Email pattern
        email_match = re.search(r'[\w\.-]+@[\w\.-]+\.\w+', content)
        if email_match:
            contact_info['email'] = email_match.group()
            
        # Fax pattern - looking for various formats
        fax_patterns = [
            r'(?:Fax|FAX):\s*(\(\d{3}\)\s*\d{3}-\d{4})',
            r'(?:Fax|FAX):\s*(\d{3}[-.]\d{3}[-.]\d{4})',
            r'(?:Fax|FAX)\s+(?:Number|#):\s*(\d{10})'
        ]
        
        for pattern in fax_patterns:
            fax_match = re.search(pattern, content)
            if fax_match:
                contact_info['fax'] = fax_match.group(1)
                break
                
        return contact_info

    async def crawl_local(self, local_data: Dict) -> LocalData:
        """Crawl a single local's website"""
        self.crawled_urls.clear()
        results = []
        local_number = str(local_data['local_union'])
        base_url = local_data['website'].lower()

        try:
            browser = await self.init_browser()
            page = await browser.new_page()
            
            # Start with base URL
            try:
                await page.goto(
                    base_url,
                    timeout=60000,
                    wait_until='domcontentloaded'  # Changed from networkidle to domcontentloaded
                )
                await asyncio.sleep(3)  # Increased sleep time
            except Exception as e:
                self.logger.error(f"Error loading {base_url}: {str(e)}")
                return LocalData(local_number=local_number, base_url=base_url)
            
            # Get all links
            links = await page.evaluate('''() => {
                return Array.from(document.links).map(link => link.href)
            }''')
            
            local_info = LocalData(
                local_number=local_number,
                base_url=base_url
            )
            
            # Process each relevant link
            for link in links:
                if self.should_follow_link(link, base_url):
                    try:
                        try:
                            await page.goto(
                                link,
                                timeout=60000,
                                wait_until='domcontentloaded'
                            )
                            await asyncio.sleep(2)
                            content = await page.content()
                            text = await page.evaluate('document.body.innerText')
                        except Exception as e:
                            self.logger.error(f"Error processing {link}: {str(e)}")
                            continue
                        
                        # Check for different types of content
                        for content_type, patterns in self.patterns.items():
                            if any(re.search(pattern, text, re.I) for pattern in patterns):
                                if content_type == 'job_board' and not local_info.job_board_url:
                                    local_info.job_board_url = link
                                elif content_type == 'resign':
                                    contact_info = await self.extract_contact_info(text)
                                    local_info.resign_email = contact_info['email']
                                    local_info.resign_fax = contact_info['fax']
                                    local_info.resign_url = link
                                elif content_type == 'referral' and not local_info.referral:
                                    local_info.referral = link
                                    
                    except Exception as e:
                        self.logger.error(f"Error processing {link}: {str(e)}")
                        
            return local_info
            
        except Exception as e:
            self.logger.error(f"Error crawling local {local_number}: {str(e)}")
            return LocalData(local_number=local_number, base_url=base_url)
            
        finally:
            await browser.close()

    async def run(self):
        """Main entry point"""
        try:
            # Load data from correct paths
            locals_file = DATA_DIR / 'outside_locals.json'
            dispatch_file = CRAWL_DIR / 'outside_locals.json'

            with open(locals_file, encoding='utf-8') as f:
                locals_data = json.load(f)
            
            with open(dispatch_file, encoding='utf-8') as f:
                dispatch_data = json.load(f)
            
            # Process just the first batch
            batch = locals_data[:self.batch_size]
                
            # Process batch concurrently
            tasks = [self.crawl_local(local) for local in batch]
            results = await asyncio.gather(*tasks)
            
            # Update dispatch data
            for result in results:
                for dispatch in dispatch_data:
                    if dispatch['local_union'] == result.local_number:
                        # Only update if we found new data
                        if result.job_board_url:
                            dispatch['job_board_url'] = result.job_board_url
                        if result.resign_url:
                            dispatch['re-sign_url'] = result.resign_url
                        if result.resign_email:
                            dispatch['re-sign_email'] = result.resign_email
                        if result.resign_fax:
                            dispatch['re-sign_fax'] = result.resign_fax
                        if result.referral:
                            dispatch['referral'] = result.referral
            
            # Save updated data
            with open(dispatch_file, 'w', encoding='utf-8') as f:
                json.dump(dispatch_data, f, indent=2)
                
            self.logger.info(f"Completed processing {len(batch)} locals")
                
        except Exception as e:
            self.logger.error(f"Crawler failed: {str(e)}")
            raise
        
        # Main execution
if __name__ == "__main__":
    crawler = SmartCrawler(bright_data_auth=BRIGHT_DATA_AUTH)
    asyncio.run(crawler.run())

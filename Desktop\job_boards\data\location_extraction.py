import json
import os

# Read the JSON data from master.json
with open('v3\data\master.json', 'r') as file:
    data = json.load(file)

# Prepare the markdown content
markdown_content = "# IBEW Local Union Locations\n\n"
markdown_content += "| Local Union | City | State |\n"
markdown_content += "|-------------|------|-------|\n"

# Extract city and state from each JSON object
for entry in data:
    local_union = entry.get('local_union', 'N/A')
    city = entry.get('city', 'N/A')
    state = entry.get('state', 'N/A')
    markdown_content += f"| {local_union} | {city} | {state} |\n"

# Write to location.md
with open('location.md', 'w') as md_file:
    md_file.write(markdown_content)

print("location.md has been created successfully.")
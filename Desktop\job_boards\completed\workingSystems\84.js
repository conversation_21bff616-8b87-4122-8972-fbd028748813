const puppeteer = require('puppeteer-core');
const path = require('path');
const { Firestore, FieldValue } = require('@google-cloud/firestore');

// Set the environment variable for Google Cloud credentials
process.env.GOOGLE_APPLICATION_CREDENTIALS = path.resolve(__dirname, 'X:/Journeyman_Jobs/v3/core/storage/config/firebase_config.json');

// Proxy Configuration (if needed)
const SBR_WS_ENDPOINT = `wss://brd-customer-hl_482339a5-zone-scraping222_111:<EMAIL>:9222`;

// Firebase Configuration
const firebaseConfig = {
  projectId: 'journeyman-jobs',
  keyFilename: path.resolve(__dirname, 'X:/Journeyman_Jobs/v3/core/storage/config/firebase_config.json')
};

const db = new Firestore(firebaseConfig);

// Function to sanitize strings for Firestore document IDs
function sanitizeString(str) {
  return str.replace(/[\/\\\.\#\$\[\]]/g, '').replace(/\s+/g, '_');
}

// Function to generate a consistent job ID
function generateJobId(localNumber, position, company) {
  return `${localNumber}-${sanitizeString(position)}-${sanitizeString(company)}`;
}

async function main() {
  console.log('Starting script...');
  const browser = await puppeteer.connect({
    browserWSEndpoint: SBR_WS_ENDPOINT,
  });

  try {
    console.log('Connected! Navigating...');
    const page = await browser.newPage();
    await page.goto('https://ibew84.workingsystems.com/workeropenjobs', {
      waitUntil: 'networkidle0'
    });

    // Wait for the table to load
    await page.waitForSelector('.wsiTable');

    // Log the HTML content of the job table
    const tableHTML = await page.evaluate(() => {
      return document.querySelector('.wsiTable').outerHTML;
    });
    console.log('Job Table HTML:', tableHTML);
    
    // Click all expand buttons to show job details (fi-plus/fi-minus toggle)
    await page.evaluate(() => {
      const buttons = document.querySelectorAll('td[data-bind="click: function() { $root.toggleDetails($data); }"] i');
      buttons.forEach(button => {
        if (button.classList.contains('fi-plus')) {
          button.click(); // Click to expand job details
        }
      });
    });

    // Extract job data
    const jobs = await page.evaluate(() => {
      const jobRows = Array.from(document.querySelectorAll('.wsiTable tbody tr'));

      return jobRows.map(row => {
        // Get the employer name
        const employer = row.querySelector('span[data-bind="text: EMPLOYER_NAME"]');
        const city = row.querySelector('span[data-bind="text: CITY"]');
        const startDate = row.querySelector('span[data-bind="text: START_DATE"]');

        // Extract expanded job details (once clicked)
        const jobClass = row.querySelector('span[data-bind="text: JOB_CLASS_DESCRIPTION"]');
        const positionsRequested = row.querySelector('span[data-bind="text: POSITIONS_REQUESTED()"]');
        const positionsAvailable = row.querySelector('span[data-bind="text: POSITIONS_REQUESTED() - POSITIONS_FILLED()"]');
        const book = row.querySelector('span[data-bind="text: BOOK_DESCRIPTION"]');
        const worksite = row.querySelector('span[data-bind="text: WORKSITE_DESCRIPTION"]');
        const hourlyWage = row.querySelector('span[data-bind="text: HOURLY_WAGE"]');
        const reportTo = row.querySelector('span[data-bind="text: REPORT_TO_LOCATION_CODE"]');
        const requestDate = row.querySelector('span[data-bind="text: REQUEST_DATE"]');
        const comments = row.querySelector('span[data-bind="text: REQUEST_NOTE"]');

        // Return the extracted job details
        return {
          employer: employer ? employer.textContent.trim() : 'N/A',
          city: city ? city.textContent.trim() : 'N/A',
          startDate: startDate ? startDate.textContent.trim() : 'N/A',
          shortCall: row.querySelector('span[data-bind="text: SHORT_CALL() == \'T\' ? \'Yes\' : \'No\'"]') ? row.querySelector('span[data-bind="text: SHORT_CALL() == \'T\' ? \'Yes\' : \'No\'"]').textContent.trim() : 'N/A',
          jobClass: jobClass ? jobClass.textContent.trim() : '',
          positionsRequested: positionsRequested ? positionsRequested.textContent.trim() : '',
          positionsAvailable: positionsAvailable ? positionsAvailable.textContent.trim() : '',
          book: book ? book.textContent.trim() : '',
          worksite: worksite ? worksite.textContent.trim() : '',
          hourlyWage: hourlyWage ? hourlyWage.textContent.trim() : '',
          reportTo: reportTo ? reportTo.textContent.trim() : '',
          requestDate: requestDate ? requestDate.textContent.trim() : '',
          comments: comments ? comments.textContent.trim() : ''
        };
      });
    });

    // Combine job data
    const combinedJobs = [
      {
        employer: jobs[0].employer,
        city: jobs[1].city,
        startDate: jobs[0].startDate,
        shortCall: jobs[0].shortCall,
        jobClass: jobs[1].jobClass,
        positionsRequested: jobs[1].positionsRequested,
        positionsAvailable: jobs[1].positionsAvailable,
        book: jobs[1].book,
        worksite: jobs[1].worksite,
        hourlyWage: jobs[1].hourlyWage,
        reportTo: jobs[1].reportTo,
        requestDate: jobs[1].requestDate,
        comments: jobs[1].comments
      },
      {
        employer: jobs[2].employer,
        city: jobs[3].city,
        startDate: jobs[2].startDate,
        shortCall: jobs[2].shortCall,
        jobClass: jobs[3].jobClass,
        positionsRequested: jobs[3].positionsRequested,
        positionsAvailable: jobs[3].positionsAvailable,
        book: jobs[3].book,
        worksite: jobs[3].worksite,
        hourlyWage: jobs[3].hourlyWage,
        reportTo: jobs[3].reportTo,
        requestDate: jobs[3].requestDate,
        comments: jobs[3].comments
      }
    ];

    console.log('Combined Job Details:', combinedJobs);

    // Extract local number (84 in this case)
    const localNumber = '84';

    // Fetch existing jobs from Firestore for the local number
    const firestoreJobsSnapshot = await db.collection('jobs').where('localNumber', '==', localNumber).get();
    const firestoreJobsMap = new Map();
    firestoreJobsSnapshot.forEach(doc => {
      firestoreJobsMap.set(doc.id, doc.data());
    });

    // Prepare a set to keep track of current job IDs
    const currentJobIds = new Set();

    // Process combined jobs
    for (const job of combinedJobs) {
      const jobId = generateJobId(localNumber, job.jobClass, job.employer);
      currentJobIds.add(jobId);

      const jobData = {
        ...job,
        localNumber,
        timestamp: firestoreJobsMap.has(jobId) ? firestoreJobsMap.get(jobId).timestamp : FieldValue.serverTimestamp()
      };

      const jobDocRef = db.collection('jobs').doc(jobId);

      if (firestoreJobsMap.has(jobId)) {
        // Update existing job without changing the initial timestamp
        await jobDocRef.update({
          ...jobData,
          timestamp: firestoreJobsMap.get(jobId).timestamp
        });
        console.log(`Updated job ${jobId}`);
      } else {
        // Create new job with initial timestamp
        await jobDocRef.set(jobData);
        console.log(`Added new job ${jobId}`);
      }
    }

    // Delete jobs from Firestore that are no longer on the website
    for (const [jobId, jobData] of firestoreJobsMap.entries()) {
      if (!currentJobIds.has(jobId)) {
        await db.collection('jobs').doc(jobId).delete();
        console.log(`Deleted outdated job ${jobId}`);
      }
    }

  } catch (err) {
    console.error('An error occurred:', err);
  }
}

if (require.main === module) {
  main().catch(err => {
    console.error(err.stack || err);
    process.exit(1);
  });
}

module.exports = main;

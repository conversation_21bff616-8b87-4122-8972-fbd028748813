import json

# Load the JSON data from the uploaded file
file_path = 'data/master.json'
with open(file_path, 'r') as file:
    data = json.load(file)

# Extract all the locals from the data
locals_list = [item["local_union"] for item in data]

# Get the total number of locals and the list of locals
total_locals = len(locals_list)

# Prepare the output data
output_data = {
    "total_locals": total_locals,
    "locals": locals_list
}

# Save to a new JSON file
output_file_path = 'data/master_count.json'
with open(output_file_path, 'w') as output_file:
    json.dump(output_data, output_file, indent=4)

output_file_path

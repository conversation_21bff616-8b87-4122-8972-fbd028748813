import json

def filter_and_count_inside_locals(input_file, output_file):
    try:
        with open(input_file, 'r') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"Error: File '{input_file}' not found.")
        return
    except json.JSONDecodeError:
        print(f"Error: Invalid JSON format in '{input_file}'.")
        return

    inside_locals = [local for local in data if "i" in local["classification"]]
    count = len(inside_locals)

    output_data = {
        "total": count,
        "locals": inside_locals
    }

    try:
        with open(output_file, 'w') as f:
            json.dump(output_data, f, indent=4)
        print(f"Successfully wrote {count} inside locals to '{output_file}'.")
    except IOError as e:
        print(f"Error writing to '{output_file}': {e}")

if __name__ == "__main__":
    input_file = 'data/master.json'
    output_file = 'data/inside_locals2.json'
    filter_and_count_inside_locals(input_file, output_file)

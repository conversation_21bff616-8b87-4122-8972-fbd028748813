import json
import re
import os

# Platform to numbers mapping
platform_mapping = {
    "wordpress": [12, 71, 89, 145, 530],
    "unionactive": [42, 44, 57, 105, 125, 196, 206, 220, 222, 245, 258, 291, 317, 379, 449, 483, 553, 649, 659, 666, 1340, 1426, 2150],
    "SquareSpace": [55],
    "workingsystems": [77, 84, 102, 104, 115, 160, 164, 193, 278, 304, 351, 443, 602, 804, 1249]
}

# Function to extract the number from a URL
def extract_number(url):
    match = re.search(r'ibew(\d+)', url)
    if match:
        return int(match.group(1))
    return None

# Read the JSON data
with open('v3\data\crawled\master_outside_urls.json', 'r') as f:
    data = json.load(f)

# Initialize a dictionary to hold URLs grouped by platform
grouped_urls = {
    "wordpress": [],
    "unionactive": [],
    "SquareSpace": [],
    "workingsystems": [],
    "unknown": []  # For URLs that don't match any platform
}

# Iterate through each item in the data
for item in data:
    main_url = item["url"]
    number = extract_number(main_url)
    # Determine the platform based on the number
    platform_found = False
    for platform, numbers in platform_mapping.items():
        if number in numbers:
            grouped_urls[platform].append(main_url)
            platform_found = True
            break
    if not platform_found:
        grouped_urls["unknown"].append(main_url)
    # Process additional URLs in the item
    for key, url in item.items():
        if key != "url":
            number = extract_number(url)
            platform_found = False
            for platform, numbers in platform_mapping.items():
                if number in numbers:
                    grouped_urls[platform].append(url)
                    platform_found = True
                    break
            if not platform_found:
                grouped_urls["unknown"].append(url)

# Prepare output in Markdown format
output = []
output.append("# Grouped URLs by Platform")
for platform, urls in grouped_urls.items():
    if platform == "unknown" and not urls:
        continue  # Skip if unknown has no URLs
    output.append(f"\n## {platform.capitalize()}")
    if not urls:
        output.append("No URLs found.")
    else:
        for url in urls:
            output.append(f"- {url}")

# Ensure the output directory exists
output_dir = "v3/data/outputs"
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

# Write the output to a file
output_file = os.path.join(output_dir, "grouped_urls.md")
with open(output_file, 'w', encoding='utf-8') as f:
    f.write('\n'.join(output))

print(f"Markdown output has been saved to {output_file}")

from typing import Dict, List, Optional, Tuple, Set
from bs4 import BeautifulSoup
import re
from dataclasses import dataclass
from datetime import datetime
import logging

@dataclass
class ContentAnalysis:
    structure_score: float
    content_score: float
    job_indicators: List[str]
    detected_fields: List[str]
    suggested_parser: str
    timestamps: List[datetime]
    classified_content: Dict[str, List[str]]

class JobBoardAnalyzer:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # IBEW-specific terms loaded from job listings
        self.ibew_terms = {
            'positions': [
                'journeyman lineman', 'apprentice lineman', 'groundman',
                'equipment operator', 'cable splicer', 'substation technician',
                'inside wireman', 'residential wireman', 'traffic signal tech'
            ],
            'credentials': [
                'cdl', 'class a', 'commercial driver', 'first aid', 'cpr', 
                'osha', 'nccco', 'eica', 'barehand', 'helo'
            ],
            'work_types': [
                'transmission', 'distribution', 'substation', 'underground',
                'overhead', 'maintenance', 'street lighting', 'traffic signals'
            ]
        }
        
        # Job board structural elements
        self.structural_elements = {
            'tables': {
                'headers': ['position', 'location', 'date', 'contractor', 'scale', 'hours'],
                'data_types': ['wage', 'hours', 'overtime', 'per diem']
            },
            'lists': {
                'classes': ['job-list', 'dispatch-list', 'work-list', 'calls'],
                'items': ['job-item', 'dispatch-item', 'position-item']
            },
            'forms': {
                'fields': ['search-jobs', 'filter', 'location', 'classification'],
                'buttons': ['apply', 'sign', 'submit', 'search']
            }
        }

    def analyze_structure(self, soup: BeautifulSoup) -> float:
        """Analyze HTML structure for job board patterns"""
        score = 0.0
        structures_found = []

        # Table analysis
        tables = soup.find_all('table')
        for table in tables:
            # Check table headers
            headers = [th.text.strip().lower() for th in table.find_all(['th', 'td'])]
            header_matches = sum(1 for h in headers 
                               if any(kh in h for kh in self.structural_elements['tables']['headers']))
            
            if header_matches >= 3:  # If we find 3+ matching headers
                score += 0.3
                structures_found.append(f"Job table found with {header_matches} matching headers")

        # List analysis
        for class_name in self.structural_elements['lists']['classes']:
            lists = soup.find_all(['ul', 'div'], class_=re.compile(class_name, re.I))
            if lists:
                score += 0.2
                structures_found.append(f"Job list structure found: {class_name}")

        # Form analysis
        forms = soup.find_all('form')
        for form in forms:
            field_matches = sum(1 for field in self.structural_elements['forms']['fields']
                              if soup.find(['input', 'select'], attrs={'name': re.compile(field, re.I)}))
            if field_matches >= 2:
                score += 0.15
                structures_found.append(f"Job search form found with {field_matches} relevant fields")

        self.logger.debug(f"Structure analysis - Score: {score}, Found: {structures_found}")
        return min(1.0, score)

    def analyze_job_indicators(self, soup: BeautifulSoup) -> Tuple[float, List[str]]:
        """Analyze content for job-related indicators"""
        score = 0.0
        indicators = []
        text = soup.get_text().lower()

        # Check for IBEW positions
        position_matches = []
        for position in self.ibew_terms['positions']:
            if position in text:
                position_matches.append(position)
                score += 0.25
        if position_matches:
            indicators.append(f"Found IBEW positions: {', '.join(position_matches)}")

        # Check for credentials
        credential_matches = []
        for cred in self.ibew_terms['credentials']:
            if cred in text:
                credential_matches.append(cred)
                score += 0.15
        if credential_matches:
            indicators.append(f"Found credentials: {', '.join(credential_matches)}")

        # Check for work types
        work_matches = []
        for work_type in self.ibew_terms['work_types']:
            if work_type in text:
                work_matches.append(work_type)
                score += 0.15
        if work_matches:
            indicators.append(f"Found work types: {', '.join(work_matches)}")

        # Look for wage patterns
        wage_patterns = [
            r'\$\d+(?:\.\d{2})?(?:/(?:hr|hour))?',
            r'scale|prevailing wage|hourly rate'
        ]
        if any(re.search(pattern, text) for pattern in wage_patterns):
            score += 0.2
            indicators.append("Found wage information")

        return min(1.0, score), indicators

    def detect_fields(self, soup: BeautifulSoup) -> List[str]:
        """Detect available job posting fields"""
        fields = set()
        text = soup.get_text().lower()

        # Common job posting fields with their indicators
        field_indicators = {
            'position': ['position', 'job title', 'classification'],
            'location': ['location', 'city', 'state', 'job site'],
            'wage': ['wage', 'pay', 'salary', 'scale', 'rate'],
            'hours': ['hours', 'schedule', 'work hours', 'shift'],
            'contractor': ['contractor', 'employer', 'company'],
            'start_date': ['start date', 'begin date', 'report date'],
            'requirements': ['requirements', 'qualifications', 'prerequisites']
        }

        for field, indicators in field_indicators.items():
            if any(ind in text for ind in indicators):
                fields.add(field)

        return list(fields)

    def extract_timestamps(self, soup: BeautifulSoup) -> List[datetime]:
        """Extract potential job posting timestamps"""
        timestamps = []
        text = soup.get_text()

        # Common date/time patterns
        patterns = [
            r'(\d{1,2}/\d{1,2}/\d{2,4})',
            r'(\d{1,2}-\d{1,2}-\d{2,4})',
            r'(\d{1,2}:\d{2}\s*(?:AM|PM))'
        ]

        for pattern in patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                try:
                    date_str = match.group(1)
                    dt = datetime.strptime(date_str, "%m/%d/%Y")
                    timestamps.append(dt)
                except ValueError:
                    continue

        return sorted(timestamps)

    def suggest_parser(self, soup: BeautifulSoup, structure_score: float, fields: List[str]) -> str:
        """Suggest the best parser based on content analysis"""
        
        # Check for platform-specific indicators
        html_str = str(soup).lower()
        
        if 'unionactive' in html_str:
            return 'unionactive'
        elif 'workingsystems' in html_str:
            return 'workingsystems'
            
        # If no specific platform detected, suggest based on structure
        if structure_score > 0.7 and len(fields) >= 5:
            return 'structured'
        
        return 'generic'

    async def analyze_content(self, html_content: str) -> ContentAnalysis:
        """Perform comprehensive content analysis"""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Run all analysis methods
        structure_score = self.analyze_structure(soup)
        content_score, indicators = self.analyze_job_indicators(soup)
        fields = self.detect_fields(soup)
        timestamps = self.extract_timestamps(soup)
        parser = self.suggest_parser(soup, structure_score, fields)
        
        # Classify content for better understanding
        classified_content = {
            'positions_found': [pos for pos in self.ibew_terms['positions'] 
                              if pos in soup.get_text().lower()],
            'credentials_found': [cred for cred in self.ibew_terms['credentials'] 
                                if cred in soup.get_text().lower()],
            'work_types_found': [wtype for wtype in self.ibew_terms['work_types'] 
                               if wtype in soup.get_text().lower()]
        }

        return ContentAnalysis(
            structure_score=structure_score,
            content_score=content_score,
            job_indicators=indicators,
            detected_fields=fields,
            suggested_parser=parser,
            timestamps=timestamps,
            classified_content=classified_content
        )
import json

def extract_outside_locals():
    with open('v3/data/master.json', 'r') as f:
        data = json.load(f)

    outside_locals = []
    for item in data:
        classification = item.get('classification', '')
        if 'o' in classification:
            outside_locals.append(item)

    with open('v3/data/outside_locals.json', 'w') as f:
        json.dump(outside_locals, f, indent=2)

if __name__ == "__main__":
    extract_outside_locals()

// Example 1: Using Puppeteer
const puppeteer = require('puppeteer-core');

async function puppeteerExample() {
    const BROWSER_WS = "wss://brd-customer-hl_482339a5-zone-scraping222_111:<EMAIL>:9222";
    try {
        console.log('Connecting to Scraping Browser...');
        const browser = await puppeteer.connect({
            browserWSEndpoint: BROWSER_WS,
        });
        const page = await browser.newPage();
        await page.goto('https://www.example.com');
        
        // CAPTCHA handling example
        // const client = await page.createCDPSession();
        // console.log('Waiting captcha to solve...');
        // const { status } = await client.send('Captcha.waitForSolve', {
        //   detectTimeout: 10000,
        // });
        // console.log('Captcha solve status:', status);
        
        const html = await page.content();
        console.log(html);
        await browser.close();
    } catch (error) {
        console.log(error)
    }
}

// Example 2: Using Playwright
const pw = require('playwright');

async function playwrightExample() {
    const AUTH = 'brd-customer-hl_482339a5-zone-scraping222_111:484fl5v89aaf';
    const SBR_CDP = `wss://${AUTH}@brd.superproxy.io:9222`;
    
    console.log('Connecting to Scraping Browser...');
    const browser = await pw.chromium.connectOverCDP(SBR_CDP);
    try {
        console.log('Connected! Navigating...');
        const page = await browser.newPage();
        await page.goto('https://example.com', { timeout: 2 * 60 * 1000 });
        
        console.log('Taking screenshot to page.png');
        await page.screenshot({ path: './page.png', fullPage: true });
        
        console.log('Navigated! Scraping page content...');
        const html = await page.content();
        console.log(html);
    } finally {
        await browser.close();
    }
}

// Example 3: Using Selenium WebDriver
const fs = require('fs/promises');
const { Builder, Browser, By } = require('selenium-webdriver');

async function seleniumExample() {
    const AUTH = 'brd-customer-hl_482339a5-zone-scraping222_111:484fl5v89aaf';
    const SBR_WEBDRIVER = `https://${AUTH}@brd.superproxy.io:9515`;
    
    const driver = await new Builder()
        .forBrowser(Browser.CHROME)
        .usingServer(SBR_WEBDRIVER)
        .build();
        
    try {
        console.log('Connected! Navigating...');
        await driver.get('https://example.com');
        
        console.log('Taking page screenshot to file page.png');
        const screenshot = await driver.takeScreenshot();
        await fs.writeFile('./page.png', Buffer.from(screenshot, 'base64'));
        
        console.log('Navigated! Scraping page content...');
        const html = await driver.getPageSource();
        console.log(html);
    } finally {
        driver.quit();
    }
}

// Run examples
if (require.main === module) {
    console.log('Running Puppeteer Example:');
    puppeteerExample()
        .then(() => {
            console.log('\nRunning Playwright Example:');
            return playwrightExample();
        })
        .then(() => {
            console.log('\nRunning Selenium Example:');
            return seleniumExample();
        })
        .catch(err => {
            console.error(err.stack || err);
            process.exit(1);
        });
}

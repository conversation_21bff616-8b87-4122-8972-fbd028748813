import json
import re

def filter_and_count_outside_locals(input_file, output_file):
    try:
        with open(input_file, 'r') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"Error: File '{input_file}' not found.")
        return
    except json.JSONDecodeError:
        print(f"Error: Invalid JSON format in '{input_file}'.")
        return

    outside_locals = []
    for item in data:
        classification = item.get('classification', '')
        # Simplified regex to catch both '(o)' and 'o' in comma-separated lists
        if re.search(r'(?:\(o\)|(?:^|,)\s*o\s*(?:,|$))', classification):
            outside_locals.append(item)
    count = len(outside_locals)

    output_data = {
        "total": count,
        "locals": outside_locals
    }

    try:
        with open(output_file, 'w') as f:
            json.dump(output_data, f, indent=4)
        print(f"Successfully wrote {count} outside locals to '{output_file}'.")
    except IOError as e:
        print(f"Error writing to '{output_file}': {e}")

if __name__ == "__main__":
    input_file = 'data/master.json'
    output_file = 'data/outside_locals2.json'
    filter_and_count_outside_locals(input_file, output_file)
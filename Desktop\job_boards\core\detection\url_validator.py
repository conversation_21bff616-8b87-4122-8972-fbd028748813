from typing import Dict, List, Optional
from dataclasses import dataclass
from urllib.parse import urlparse
import os
import re
import json
import logging
from pathlib import Path

@dataclass
class ValidationResult:
    score: float
    confidence: float
    platform: Optional[str]
    flags: List[str]
    local_number: Optional[str]
    classification: Optional[str]

class IBEWUrlValidator:
    """Core URL validation using IBEW patterns and platforms"""
    
    def __init__(self):
        outside_locals_path = 'X:/Journeyman_Jobs/v3/core/path_to_your_file/outside_locals.json' 
        
        self.outside_locals: Dict[str, str] = {}
        self.known_classifications: Dict[str, str] = {}
        self.logger = logging.getLogger(__name__)
        
        # Load outside locals data
        self.load_outside_locals(outside_locals_path)
        
        # Log output directory
        self.output_dir = 'X:/Journeyman_Jobs/v3/output/logs'
        os.makedirs(self.output_dir, exist_ok=True)  # Create directory if it doesn't exist

        # Platform patterns
        self.platform_patterns = {
            'unionactive': [
                r'unionactive\.com',
                r'\/unionactive\/',
                r'dispatch_view\.cfm',
                r'jobcalls_view\.cfm'
            ],
            'workingsystems': [
                r'workingsystems\.com',
                r'\/dispatch\/',
                r'\/jobs\/',
                r'workeropenjobs'
            ],
            'generic_job_patterns': [
                r'\/jobs?\/?$',
                r'\/careers?\/?$',
                r'\/dispatch\/?$',
                r'\/referr?al\/?$',
                r'\/work-?list\/?$',
                r'\/job-?calls?\/?$'
            ]
        }
        
    def load_outside_locals(self, filepath: str) -> None:
        """Load data from outside_locals.json"""
        try:
            with open(filepath, 'r') as f:
                data = json.load(f)
                for item in data:
                    if 'local' in item and 'url' in item:
                        self.outside_locals[item['local']] = item['url'].lower()
                    if 'local' in item and 'classification' in item:
                        self.known_classifications[item['local']] = item['classification']
                        
            self.logger.info(f"Loaded {len(self.outside_locals)} locals from outside_locals.json")
            
        except Exception as e:
            self.logger.error(f"Error loading outside locals: {str(e)}")
            raise
            
    def extract_local_number(self, url: str) -> Optional[str]:
        """Extract local number from URL using known patterns"""
        patterns = [
            r'ibew(?:local)?(\d+)',
            r'local(?:union)?(\d+)',
            r'lu?(\d+)',
            r'\/(\d+)\/'
        ]
        
        url_lower = url.lower()
        for pattern in patterns:
            match = re.search(pattern, url_lower)
            if match:
                return match.group(1)
        return None

    def validate_url(self, url: str, local_number: Optional[str] = None) -> ValidationResult:
        """Validate a URL against known patterns"""
        url_lower = url.lower()
        flags = []
        score = 0.0
        
        # Check if URL is a known homepage
        if local_number and local_number in self.outside_locals:
            if url_lower == self.outside_locals[local_number].lower():
                flags.append('known_homepage')
                score += 0.3
        
        # Extract and validate local number if not provided
        extracted_local = local_number or self.extract_local_number(url)
        if extracted_local:
            flags.append(f'local_number_found_{extracted_local}')
            score += 0.2
        
        # Platform detection
        platform = self.detect_platform(url)
        if platform:
            score += 0.2
            flags.append(f'platform_{platform}')
            
            # Check for job-specific patterns
            if self.has_job_patterns(url, platform):
                score += 0.3
                flags.append('job_patterns_found')
        
        # Normalize final score
        final_score = min(1.0, score)
        
        return ValidationResult(
            score=final_score,
            confidence=self.calculate_confidence(final_score, flags),
            platform=platform,
            flags=flags,
            local_number=extracted_local,
            classification=self.known_classifications.get(extracted_local)
        )
    
    def detect_platform(self, url: str) -> Optional[str]:
        """Detect which platform the URL belongs to"""
        url_lower = url.lower()
        
        for platform, patterns in self.platform_patterns.items():
            if any(re.search(pattern, url_lower) for pattern in patterns):
                return platform
        return None
        
    def has_job_patterns(self, url: str, platform: str) -> bool:
        """Check if URL contains platform-specific job patterns"""
        url_lower = url.lower()
        
        # Check platform-specific patterns first
        if platform in self.platform_patterns:
            if any(re.search(pattern, url_lower) for pattern in self.platform_patterns[platform]):
                return True
            
        # Check generic patterns
        return any(
            re.search(pattern, url_lower) 
            for pattern in self.platform_patterns['generic_job_patterns']
        )
        
    def calculate_confidence(self, score: float, flags: List[str]) -> float:
        """Calculate confidence level based on score and validation flags"""
        confidence = score
        
        # Adjust confidence based on flags
        if 'known_homepage' in flags:
            confidence *= 0.8
        elif 'platform_unionactive' in flags or 'platform_workingsystems' in flags:
            confidence *= 1.1
            
        return min(1.0, confidence)
    
    def log_results(self, result: ValidationResult) -> None:
        """Log validation results to a file"""
        log_file = os.path.join(self.output_dir, 'validation_results.log')

        with open(log_file, 'a') as f:
            f.write(f"{json.dumps(result.__dict__)}\n")
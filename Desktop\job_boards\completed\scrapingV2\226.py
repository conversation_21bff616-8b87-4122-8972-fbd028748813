import json
import asyncio
import os
import logging
from tenacity import retry, stop_after_attempt, wait_exponential
from crawl4ai import Async<PERSON>ebCraw<PERSON>
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy, LLMExtractionStrategy
from google.cloud import firestore

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    filename='scraper.log'
)
logger = logging.getLogger('ibew226_scraper')

# Local number
LOCAL_NUMBER = '226'

def validate_job_data(job):
    required_fields = ['company', 'job_class', 'location']
    return all(job.get(field) and isinstance(job[field], str) and job[field].strip() for field in required_fields)

def sanitize_string(s):
    if not s:
        return ''
    return s.replace('/', '_').replace('\\', '_').replace('.', '_').replace('#', '_').replace('$', '_').replace('[', '_').replace(']', '_').replace(' ', '_')

def generate_job_id(local_number, job_class, company):
    sanitized_job_class = sanitize_string(job_class)
    sanitized_company = sanitize_string(company)
    return f"{local_number}-{sanitized_job_class}-{sanitized_company}"

@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
async def update_firestore(jobs):
    db = firestore.AsyncClient(project='journeyman-jobs')
    current_job_ids = set()

    # Get existing jobs
    query = db.collection('jobs').where('localNumber', '==', LOCAL_NUMBER)
    existing_jobs = {}
    async for doc in query.stream():
        existing_jobs[doc.id] = doc.to_dict()

    logger.info(f"Found {len(existing_jobs)} existing jobs in Firestore")

    # Update or add jobs
    for job in jobs:
        # Map fields to standardized schema
        standardized_job = {
            'company': job.get('company', 'N/A'),
            'job_class': job.get('title', 'N/A'),
            'location': job.get('location', 'N/A'),
            'pay': job.get('pay', 'N/A'),
            'description': job.get('description', 'N/A')
        }

        if not validate_job_data(standardized_job):
            logger.warning(f"Invalid job data: {standardized_job}. Skipping.")
            continue

        job_id = generate_job_id(LOCAL_NUMBER, standardized_job['job_class'], standardized_job['company'])
        current_job_ids.add(job_id)

        job_data = {
            'jobId': job_id,
            'localNumber': LOCAL_NUMBER,
            'employer': standardized_job['company'],
            'jobClass': standardized_job['job_class'],
            'location': standardized_job['location'],
            'wage': standardized_job['pay'],
            'startDate': 'N/A',
            'description': standardized_job['description'],
        }

        doc_ref = db.collection('jobs').document(job_id)

        if job_id in existing_jobs:
            # Preserve the original timestamp
            job_data['timestamp'] = existing_jobs[job_id].get('timestamp')
            await doc_ref.update(job_data)
            logger.info(f"Updated job {job_id}")
        else:
            # Set server timestamp for new jobs
            job_data['timestamp'] = firestore.SERVER_TIMESTAMP
            await doc_ref.set(job_data)
            logger.info(f"Added new job {job_id}")

    # Delete outdated jobs
    for job_id in existing_jobs:
        if job_id not in current_job_ids:
            await db.collection('jobs').document(job_id).delete()
            logger.info(f"Deleted outdated job {job_id}")

@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
async def run_llm_extraction(crawler, url):
    llm_strategy = LLMExtractionStrategy(
        provider="openai/gpt-4o",
        api_token=os.getenv('OPENAI_API_KEY'),
        instruction="Analyze the job listings to classify their classification, pay, and location.",
        extraction_type="schema"
    )
    return await crawler.arun(url=url, extraction_strategy=llm_strategy, bypass_cache=True)

async def extract_job_data():
    logger.info('Starting job scraping for Local 226')
    print('Starting job scraping for Local 226...')

    # Define the CSS extraction schema for job listings
    css_schema = {
        "name": "Job Listings",
        "baseSelector": ".job-posting", # Adjusted based on actual DOM
        "fields": [
            {
                "name": "title",
                "selector": ".job-title",
                "type": "text"
            },
            {
                "name": "company",
                "selector": ".company-name",
                "type": "text"
            },
            {
                "name": "location",
                "selector": ".job-location",
                "type": "text"
            },
            {
                "name": "pay",
                "selector": ".job-pay",
                "type": "text"
            },
            {
                "name": "description",
                "selector": ".job-description",
                "type": "text"
            }
        ]
    }

    # Create the CSS extraction strategy
    css_strategy = JsonCssExtractionStrategy(css_schema, verbose=True)

    try:
        async with AsyncWebCrawler(verbose=True) as crawler:
            # First, extract structured data using CSS strategy with retry
            css_result = await retry(
                stop=stop_after_attempt(3),
                wait=wait_exponential(multiplier=1, min=4, max=10)
            )(crawler.arun)(
                url="https://www.ibew226.com/ibew226_dir/Jobs",
                extraction_strategy=css_strategy,
                bypass_cache=True,
            )

            # Ensure the CSS extraction was successful
            if not css_result.success:
                logger.error("Failed to crawl and extract job listings using CSS strategy.")
                print("Failed to crawl and extract job listings using CSS strategy.")
                return

            # Parse the extracted content
            jobs = json.loads(css_result.extracted_content)
            logger.info(f"Successfully extracted {len(jobs)} job listings using CSS strategy.")
            print(f"Successfully extracted {len(jobs)} job listings using CSS strategy.")

            # Save extracted jobs to Firestore
            await update_firestore(jobs)
            logger.info("Jobs updated in Firestore")
            print("Jobs updated in Firestore")

            # Ensure the output directory exists for local storage
            output_dir = ".outputs/scrapers/"
            os.makedirs(output_dir, exist_ok=True)

            # Save results to JSON file
            with open(os.path.join(output_dir, "css_extracted_jobs.json"), "w", encoding="utf-8") as f:
                json.dump(jobs, f, indent=2)
            logger.info("Saved extracted jobs to local JSON file")
            print("Saved extracted jobs to local JSON file")

    except Exception as e:
        logger.error(f"Error during job extraction: {str(e)}", exc_info=True)
        print(f"Error during job extraction: {str(e)}")
        raise

# Run the async function
if __name__ == '__main__':
    try:
        asyncio.run(extract_job_data())
    except Exception as e:
        logger.error(f"Script failed: {str(e)}", exc_info=True)
        print(f"Script failed: {str(e)}")
        exit(1)
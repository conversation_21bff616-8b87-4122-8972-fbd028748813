const puppeteer = require('puppeteer-core');
const path = require('path');

// Proxy Configuration (if needed)
const SBR_WS_ENDPOINT = `wss://brd-customer-hl_482339a5-zone-scraping222_111:<EMAIL>:9222`;

// Function to sanitize strings for use in document IDs
function sanitizeString(str) {
  // Replace characters that might cause issues in document IDs (Firebase limitations)
  // Also replace spaces with underscores
  return str.replace(/[\/\\\.\#\$\[\]]/g, '').replace(/\s+/g, '_');
}

// Function to generate a consistent job ID based on local number, classification, and employer
function generateJobId(localNumber, classification, employer) {
  // Ensure classification and employer are not empty or null before sanitizing
  const sanitizedClassification = classification ? sanitizeString(classification) : 'unknown_classification';
  const sanitizedEmployer = employer ? sanitizeString(employer) : 'unknown_employer';

  return `${localNumber}-${sanitizedClassification}-${sanitizedEmployer}`;
}

async function main() {
  console.log('Starting script...');
  let browser; // Declare browser variable outside try for finally block access

  try {
    console.log('Connecting to browser...');
    browser = await puppeteer.connect({
      browserWSEndpoint: SBR_WS_ENDPOINT,
    });

    console.log('Connected! Navigating to job list...');
    const page = await browser.newPage();
    // Navigate to the job listings page using the CORRECTED URL
    await page.goto('https://www.ibew125.com/index.cfm?zone=/unionactive/view_article.cfm&HomeID=710029&page=Dispatch', {
      waitUntil: 'networkidle0' // Wait until network is idle
    });

    // Extract initial job data (title, link, description) from the list page
    const initialJobs = await page.evaluate(() => {
      // Select all rows that represent a job listing
      const jobRows = Array.from(document.querySelectorAll('table tbody tr.resprow'));

      return jobRows.map(row => {
        const titleElement = row.querySelector('a'); // Element containing the job title and link
        const descriptionElement = row.querySelector('div.w3-card'); // Element containing the job description

        if (titleElement && descriptionElement) {
          return {
            title: titleElement.textContent.trim(),
            link: titleElement.href,
            description: descriptionElement.textContent.trim()
          };
        }
        return null; // Return null for rows that don't match the expected structure
      }).filter(job => job !== null); // Filter out any null entries
    });

    console.log('Scraped Initial Job Details from list page:');
    console.log(JSON.stringify(initialJobs, null, 2));

    if (initialJobs.length === 0) {
      console.log('No jobs found on the list page. Double-check the page structure and selectors.');
      return; // Exit the function if no jobs are found
    }

    // Extract local number (in this case, using '125' as an example)
    const localNumber = '125';

    // Use a Map to store unique jobs, keyed by the desired Job ID format
    const uniqueJobs = new Map();

    console.log('\nProcessing jobs and scraping detailed information...');

    // Process each initial job entry to get detailed info and generate the unique ID
    for (const job of initialJobs) {
      console.log(`Navigating to detailed page for: ${job.title}`);

      // Navigate to the job listing link and scrape detailed information
      // Use a try-catch block for navigation in case of broken links
      try {
        await page.goto(job.link, { waitUntil: 'networkidle0' });

        const detailedData = await page.evaluate(() => {
          // *** IMPORTANT: You need to inspect the detailed job page HTML
          // and find the correct selectors for 'classification' and 'employer'.
          // Replace the placeholder selectors below with the actual ones. ***

          // Example placeholders - REPLACE THESE
          const classificationElement = document.querySelector('.job-classification-selector'); // <-- Replace with actual selector
          const employerElement = document.querySelector('.job-employer-selector'); // <-- Replace with actual selector
          const fullDescriptionElement = document.querySelector('div.w3-card'); // Assuming this is the main content area

          const classification = classificationElement ? classificationElement.textContent.trim() : 'Unknown Classification';
          const employer = employerElement ? employerElement.textContent.trim() : 'Unknown Employer';
          const detailedDescription = fullDescriptionElement ? fullDescriptionElement.textContent.trim() : 'No detailed description found.';


          return {
            classification: classification,
            employer: employer,
            detailedDescription: detailedDescription
          };
        });

        // Generate the Job ID using the extracted classification and employer
        const jobId = generateJobId(localNumber, detailedData.classification, detailedData.employer);

        // Combine initial and detailed data
        const fullJobData = {
          jobId: jobId, // Store the generated ID in the job data
          title: job.title, // Keep the title from the list page
          link: job.link,
          initialDescription: job.description, // Keep the description from the list page
          detailedDescription: detailedData.detailedDescription, // Add the detailed description
          classification: detailedData.classification, // Add the scraped classification
          employer: detailedData.employer, // Add the scraped employer
          localNumber: localNumber,
          timestamp: new Date().toISOString() // Use current timestamp for in-memory storage
        };

        // Add the job to the map using the generated jobId.
        // If a job with this ID already exists (due to duplicate initial listings
        // pointing to the same classification/employer), it will be overwritten
        // with the latest scraped data for that unique job.
        uniqueJobs.set(jobId, fullJobData);

        console.log(`Scraped detailed information and generated ID for: ${jobId}`);
        console.log('-'.repeat(80));

        // Return to the main list page to process the next job
        await page.goBack();

      } catch (navError) {
        console.error(`Could not navigate to or scrape detailed info for job ${job.title} (${job.link}): ${navError}`);
        console.log('-'.repeat(80));
        // If navigation fails, stay on the current page and proceed to the next job
      }
    }

    // Print summary of all unique jobs found
    console.log('\nSummary of all unique jobs:');
    console.log('='.repeat(80));
    if (uniqueJobs.size === 0) {
        console.log("No unique jobs processed.");
    } else {
        for (const [jobId, jobData] of uniqueJobs.entries()) {
          console.log(`Job ID: ${jobId}`);
          console.log(`Title: ${jobData.title}`);
          console.log(`Classification: ${jobData.classification}`);
          console.log(`Employer: ${jobData.employer}`);
          console.log(`Link: ${jobData.link}`);
          console.log(`Local Number: ${jobData.localNumber}`);
          console.log(`Timestamp: ${jobData.timestamp}`);
          // You can choose to print initialDescription or detailedDescription here if needed
          // console.log(`Initial Description: ${jobData.initialDescription}`);
          // console.log(`Detailed Description: ${jobData.detailedDescription}`);
          console.log('-'.repeat(40));
        }
    }


  } catch (err) {
    console.error('An error occurred during the scraping process:', err);
  } finally {
    // Ensure the browser is closed even if an error occurs
    if (browser) {
      await browser.close();
      console.log('Browser closed.');
    }
    console.log('Script finished.');
  }
}

// Run the main function if the script is executed directly
if (require.main === module) {
  main().catch(err => {
    console.error('Unhandled error:', err.stack || err);
    process.exit(1);
  });
}

// Export the main function (useful if you want to import this script elsewhere)
module.exports = main;


from typing import Dict, List, Optional, Set
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.ensemble import RandomForestClassifier
import joblib
from bs4 import BeautifulSoup
import re
import logging
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path

@dataclass
class DetectionResult:
    """Stores the result of a job board detection analysis"""
    is_job_board: bool
    confidence: float
    features_found: Dict[str, any]
    ml_score: float
    pattern_score: float
    platform: Optional[str]
    suggested_crawl_time: Optional[datetime] = None

class MLJobBoardDetector:
    """Intelligent job board detection using ML and pattern matching"""
    
    def __init__(self, model_path: Optional[str] = None):
        self.vectorizer = TfidfVectorizer(
            max_features=1000,
            ngram_range=(1, 3),
            stop_words='english'
        )
        self.classifier = RandomForestClassifier(
            n_estimators=100,
            min_samples_leaf=2,
            n_jobs=-1
        )
        self.logger = logging.getLogger(__name__)
        
        # Load existing model if available
        if model_path and Path(model_path).exists():
            self.load_model(model_path)
            
        # Known patterns from IBEW data
        self.job_patterns = {
            'structural': [
                r'<table[^>]*class=["\'].*?job.*?["\']',
                r'<div[^>]*class=["\'].*?dispatch.*?["\']',
                r'<form[^>]*class=["\'].*?referral.*?["\']'
            ],
            'content': [
                r'(?i)job\s*calls?',
                r'(?i)dispatch\s*list',
                r'(?i)work\s*list',
                r'(?i)available\s*positions?',
                r'(?i)referr?al\s*system'
            ],
            'navigation': [
                r'<a[^>]*>.*?job.*?</a>',
                r'<a[^>]*>.*?dispatch.*?</a>',
                r'<a[^>]*>.*?referral.*?</a>'
            ]
        }
        
        # Classification terms from outside_locals.json
        self.classification_terms = {
            'inside_terms': ['inside', 'residential', 'wireman'],
            'outside_terms': ['outside', 'lineman', 'groundman', 'operator'],
            'specialty_terms': ['traffic', 'signal', 'technician', 'substation']
        }
        
    def extract_features(self, html_content: str) -> Dict:
        """Extract features from HTML content for ML and pattern matching"""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Text features
        nav_text = ' '.join([nav.get_text() for nav in soup.find_all('nav')])
        headers = ' '.join([h.get_text() for h in soup.find_all(['h1', 'h2', 'h3'])])
        links = ' '.join([a.get_text() for a in soup.find_all('a')])
        
        # Structure features
        features = {
            'has_tables': len(soup.find_all('table')) > 0,
            'has_forms': len(soup.find_all('form')) > 0,
            'num_links': len(soup.find_all('a')),
            'text_content': f"{nav_text} {headers} {links}".lower(),
            
            # Pattern matches
            'structural_matches': sum(
                bool(re.search(pattern, html_content)) 
                for pattern in self.job_patterns['structural']
            ),
            'content_matches': sum(
                bool(re.search(pattern, html_content)) 
                for pattern in self.job_patterns['content']
            ),
            'navigation_matches': sum(
                bool(re.search(pattern, html_content)) 
                for pattern in self.job_patterns['navigation']
            ),
            
            # Classification matches
            'inside_terms': sum(
                term in html_content.lower() 
                for term in self.classification_terms['inside_terms']
            ),
            'outside_terms': sum(
                term in html_content.lower() 
                for term in self.classification_terms['outside_terms']
            ),
            'specialty_terms': sum(
                term in html_content.lower() 
                for term in self.classification_terms['specialty_terms']
            )
        }
        
        # Look for time patterns (for scheduling)
        time_patterns = [
            r'\d{1,2}:\d{2}\s*(?:AM|PM|am|pm)',
            r'\d{1,2}\s*(?:AM|PM|am|pm)'
        ]
        features['has_time_patterns'] = any(
            re.search(pattern, html_content)
            for pattern in time_patterns
        )
        
        return features
        
    def prepare_training_data(self, features: Dict) -> np.ndarray:
        """Prepare feature dictionary for ML model"""
        # Convert text to TF-IDF
        text_features = self.vectorizer.transform([features['text_content']])
        
        # Prepare numerical features
        numerical = np.array([
            features['has_tables'],
            features['has_forms'],
            features['num_links'],
            features['structural_matches'],
            features['content_matches'],
            features['navigation_matches'],
            features['inside_terms'],
            features['outside_terms'],
            features['specialty_terms']
        ])
        
        # Combine features
        return np.hstack([text_features.toarray(), numerical.reshape(1, -1)])
        
    async def detect_job_board(self, html_content: str, url: str) -> DetectionResult:
        """Analyze a page to determine if it's a job board"""
        features = self.extract_features(html_content)
        
        # Get ML prediction if model is trained
        if hasattr(self.vectorizer, 'vocabulary_'):
            X = self.prepare_training_data(features)
            ml_score = self.classifier.predict_proba(X)[0][1]
        else:
            ml_score = 0.0
            
        # Calculate pattern matching score
        pattern_score = (
            (features['structural_matches'] * 0.4) +
            (features['content_matches'] * 0.4) +
            (features['navigation_matches'] * 0.2)
        ) / (len(self.job_patterns['structural']) + 
             len(self.job_patterns['content']) + 
             len(self.job_patterns['navigation']))
        
        # Combine scores with weights
        confidence = (ml_score * 0.6) + (pattern_score * 0.4)
        
        return DetectionResult(
            is_job_board=confidence > 0.5,
            confidence=confidence,
            features_found=features,
            ml_score=ml_score,
            pattern_score=pattern_score,
            platform=self.detect_platform(html_content, url),
            suggested_crawl_time=self.extract_suggested_time(html_content)
        )
        
    def detect_platform(self, html_content: str, url: str) -> Optional[str]:
        """Detect which platform/CMS the page is using"""
        url_lower = url.lower()
        html_lower = html_content.lower()
        
        if 'unionactive' in url_lower or 'unionactive' in html_lower:
            return 'unionactive'
        elif 'workingsystems' in url_lower or 'workingsystems' in html_lower:
            return 'workingsystems'
        
        # Add more platform detection logic here
        return None
        
    def extract_suggested_time(self, html_content: str) -> Optional[datetime]:
        """Extract suggested crawl time from page content"""
        # Look for time patterns
        time_patterns = [
            r'(\d{1,2}):(\d{2})\s*(AM|PM|am|pm)',
            r'(\d{1,2})\s*(AM|PM|am|pm)'
        ]
        
        for pattern in time_patterns:
            match = re.search(pattern, html_content)
            if match:
                try:
                    # Parse time components
                    if len(match.groups()) == 3:  # HH:MM AM/PM
                        hour = int(match.group(1))
                        minute = int(match.group(2))
                        meridiem = match.group(3).upper()
                    else:  # HH AM/PM
                        hour = int(match.group(1))
                        minute = 0
                        meridiem = match.group(2).upper()
                        
                    # Convert to 24-hour format
                    if meridiem == 'PM' and hour < 12:
                        hour += 12
                    elif meridiem == 'AM' and hour == 12:
                        hour = 0
                        
                    return datetime.now().replace(
                        hour=hour,
                        minute=minute,
                        second=0,
                        microsecond=0
                    )
                except ValueError:
                    continue
                    
        return None
        
    def train(self, training_data: List[Dict[str, any]]) -> None:
        """Train the ML model with examples"""
        X_text = []
        X_numerical = []
        y = []
        
        for item in training_data:
            features = self.extract_features(item['html_content'])
            X_text.append(features['text_content'])
            
            X_numerical.append([
                features['has_tables'],
                features['has_forms'],
                features['num_links'],
                features['structural_matches'],
                features['content_matches'],
                features['navigation_matches'],
                features['inside_terms'],
                features['outside_terms'],
                features['specialty_terms']
            ])
            
            y.append(item['is_job_board'])
            
        # Transform text features
        X_text_tfidf = self.vectorizer.fit_transform(X_text)
        
        # Combine features
        X = np.hstack([X_text_tfidf.toarray(), np.array(X_numerical)])
        
        # Train classifier
        self.classifier.fit(X, y)
        self.logger.info("Model training completed")
        
    def save_model(self, path: str) -> None:
        """Save the trained model to disk"""
        joblib.dump({
            'vectorizer': self.vectorizer,
            'classifier': self.classifier
        }, path)
        self.logger.info(f"Model saved to {path}")
        
    def load_model(self, path: str) -> None:
        """Load a trained model from disk"""
        try:
            saved = joblib.load(path)
            self.vectorizer = saved['vectorizer']
            self.classifier = saved['classifier']
            self.logger.info(f"Model loaded from {path}")
        except Exception as e:
            self.logger.error(f"Error loading model: {str(e)}")
            raise
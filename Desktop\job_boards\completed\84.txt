[{"Key": "<strong class=\"\"></strong>", "Field2": "<th class=\"\">&nbsp;\n                  \n                </th>"}, {"Key": "<strong class=\"\"><span data-bind=\"text: bidOrView\" class=\"\">Bidding</span> is Closed</strong>", "Field2": "<th style=\"cursor: pointer\" data-bind=\"click: function () { sortToggle('employer'); }\" class=\"\">\n                  Employer \n                  <i data-bind=\"css: {'fi-arrow-up': $root.sortKey() == 'employer' &amp;&amp; $root.sortDirection() == 1, 'fi-arrow-down':  $root.sortKey() == 'employer' &amp;&amp; $root.sortDirection() == -1 }\" class=\"\"></i>\n                </th>"}, {"Key": "<strong class=\"\">Bidding is Open</strong>", "Field2": "<th style=\"cursor: pointer\" data-bind=\"click: function () { sortToggle('city'); }\" class=\"\">\n                  City\n                  <i data-bind=\"css: {'fi-arrow-up': $root.sortKey() == 'city' &amp;&amp; $root.sortDirection() == 1, 'fi-arrow-down':  $root.sortKey() == 'city' &amp;&amp; $root.sortDirection() == -1 }\" class=\"\"></i>\n                </th>"}, {"Key": "<strong class=\"\">Viewing is Open</strong>", "Field2": "<th class=\"tc\" style=\"cursor: pointer\" data-bind=\"click: function () { sortToggle('start_date'); }\">\n                  Start Date\n                  <i data-bind=\"css: {'fi-arrow-up': $root.sortKey() == 'start_date' &amp;&amp; $root.sortDirection() == 1, 'fi-arrow-down':  $root.sortKey() == 'start_date' &amp;&amp; $root.sortDirection() == -1 }\" class=\"\"></i>\n                </th>"}, {"Key": "<strong class=\"\">Administrative View</strong>", "Field2": "<th class=\"tc\" data-bind=\"click: function () { sortToggle('short_call'); }\">Short call</th>"}, {"Key": "<strong class=\"\">You must login to bid.</strong>", "Field2": "<span data-bind=\"text: JOB_CLASS_DESCRIPTION\" class=\"\">SUBSTATION TECH</span>"}, {"Key": "<strong class=\"\">Job Class: </strong>", "Field2": "<span data-bind=\"text: POSITIONS_REQUESTED()\" class=\"\">2</span>"}, {"Key": "<strong class=\"\">Positions Requested: </strong>", "Field2": "<span data-bind=\"text: POSITIONS_REQUESTED() - POSITIONS_FILLED()\" class=\"\">1</span>"}, {"Key": "<strong class=\"\">Positions Available: </strong>", "Field2": "<span data-bind=\"text: BOOK_DESCRIPTION\" class=\"\">SUBSTATION TECHNICIAN</span>"}, {"Key": "<strong class=\"\">Book: </strong>", "Field2": "<span data-bind=\"text: JOB_DURATION\" class=\"\"></span>"}, {"Key": "<strong class=\"\">Duration: </strong>", "Field2": "<span data-bind=\"text: COMMENT\" class=\"\"></span>"}, {"Key": "<strong class=\"\">Comment: </strong>", "Field2": "<span data-bind=\"text: <PERSON><PERSON><PERSON>\" class=\"\"></span>"}, {"Key": "<strong class=\"\">Requirements: </strong>", "Field2": "<span data-bind=\"text: (SKILLS() &amp;&amp; CONDITIONS()) ? ', ' : ''\" class=\"\"></span>"}, {"Key": "<strong class=\"\">Report In: </strong>", "Field2": "<span data-bind=\"text: CONDITIONS\" class=\"\"></span>"}, {"Key": "<strong class=\"\">Worksite: </strong>", "Field2": "<span data-bind=\"text: REPORT_IN_TIME\" class=\"\"></span>"}, {"Key": "<strong class=\"\">Report To: </strong>", "Field2": "<span data-bind=\"text: WOR<PERSON>ITE_DESCRIPTION\" class=\"\">Statewide</span>"}, {"Key": "<strong class=\"\">Hourly Wage: </strong>", "Field2": "<span data-bind=\"text: REPORT_TO_LOCATION_CODE\" class=\"\">Statewide</span>"}, {"Key": "<strong class=\"\">Checkmark: </strong>", "Field2": "<span data-bind=\"text: HOURLY_WAGE\" class=\"\">40.33</span>"}, {"Key": "<strong class=\"\">Region: </strong>", "Field2": "<span data-bind=\"text: REJECTION_PENALTY() == 'T' ? 'Yes' : 'No'\" class=\"\">No</span>"}, {"Key": "<strong class=\"\">Request Date: </strong>", "Field2": "<span data-bind=\"text: REGION_CODE\" class=\"\"></span>"}, {"Key": "<strong class=\"\">Comments: </strong>", "Field2": "<span data-bind=\"text: REQUEST_DATE\" class=\"\">12/05/2024</span>"}, {"Key": "<strong class=\"\">Job Class: </strong>", "Field2": "<span data-bind=\"text: REQUEST_NOTE\" class=\"\">Start date: 12/16/24; 4-10s; Location: STATEWIDE, Per diem: $100, Contact: <PERSON> 404-615-0571</span>"}, {"Key": "<strong class=\"\">Positions Requested: </strong>", "Field2": "<span data-bind=\"text: JOB_CLASS_DESCRIPTION\" class=\"\">SUBSTATION TECH</span>"}, {"Key": "<strong class=\"\">Positions Available: </strong>", "Field2": "<span data-bind=\"text: POSITIONS_REQUESTED()\" class=\"\">1</span>"}, {"Key": "<strong class=\"\">Book: </strong>", "Field2": "<span data-bind=\"text: POSITIONS_REQUESTED() - POSITIONS_FILLED()\" class=\"\">1</span>"}, {"Key": "<strong class=\"\">Duration: </strong>", "Field2": "<span data-bind=\"text: BOOK_DESCRIPTION\" class=\"\">SUBSTATION TECHNICIAN</span>"}, {"Key": "<strong class=\"\">Comment: </strong>", "Field2": "<span data-bind=\"text: JOB_DURATION\" class=\"\"></span>"}, {"Key": "<strong class=\"\">Requirements: </strong>", "Field2": "<span data-bind=\"text: COMMENT\" class=\"\"></span>"}, {"Key": "<strong class=\"\">Report In: </strong>", "Field2": "<span data-bind=\"text: <PERSON><PERSON><PERSON>\" class=\"\"></span>"}, {"Key": "<strong class=\"\">Worksite: </strong>", "Field2": "<span data-bind=\"text: (SKILLS() &amp;&amp; CONDITIONS()) ? ', ' : ''\" class=\"\"></span>"}, {"Key": "<strong class=\"\">Report To: </strong>", "Field2": "<span data-bind=\"text: CONDITIONS\" class=\"\"></span>"}, {"Key": "<strong class=\"\">Hourly Wage: </strong>", "Field2": "<span data-bind=\"text: REPORT_IN_TIME\" class=\"\"></span>"}, {"Key": "<strong class=\"\">Checkmark: </strong>", "Field2": "<span data-bind=\"text: WOR<PERSON>ITE_DESCRIPTION\" class=\"\">Statewide</span>"}, {"Key": "<strong class=\"\">Region: </strong>", "Field2": "<span data-bind=\"text: REPORT_TO_LOCATION_CODE\" class=\"\">Statewide</span>"}, {"Key": "<strong class=\"\">Request Date: </strong>", "Field2": "<span data-bind=\"text: HOURLY_WAGE\" class=\"\">40.33</span>"}, {"Key": "<strong class=\"\">Comments: </strong>", "Field2": "<span data-bind=\"text: REJECTION_PENALTY() == 'T' ? 'Yes' : 'No'\" class=\"\">No</span>"}, {"Key": "<strong class=\"\">Job Class: </strong>", "Field2": "<span data-bind=\"text: REGION_CODE\" class=\"\"></span>"}, {"Key": "<strong class=\"\">Positions Requested: </strong>", "Field2": "<span data-bind=\"text: REQUEST_DATE\" class=\"\">11/11/2024</span>"}, {"Key": "<strong class=\"\">Positions Available: </strong>", "Field2": "<span data-bind=\"text: REQUEST_NOTE\" class=\"\">Sub. Tech. for statewide work. No per diem.,</span>"}, {"Key": "<strong class=\"\">Book: </strong>", "Field2": "<th colspan=\"2\" style=\"cursor: pointer; vertical-align:center;\" class=\"\">\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t<!-- ko if: $root.authenticated() && $root.jobsAvailable() && $root.booleanBidding() --><!-- /ko -->\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t<!-- ko if: $root.authenticated() && $root.jobsAvailable() && $root.numericBidding() --><!-- /ko -->\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t<span data-bind=\"text: EMPLOYER_NAME, click: function () { $root.sortToggle('employer'); }\" class=\"\">INCEPTION ENERGY SOLUTIONS</span>\n\t\t\t\t\t\t\t\t\t<i data-bind=\"css: {'fi-arrow-up': $root.sortKey() == 'employer' &amp;&amp; $root.sortDirection() == 1, 'fi-arrow-down':  $root.sortKey() == 'employer' &amp;&amp; $root.sortDirection() == -1 }\" class=\"\"></i>\n\t\t\t\t\t\t\t\t</th>"}, {"Key": "<strong class=\"\">Duration: </strong>", "Field2": "<th style=\"cursor: pointer\" data-bind=\"click: function () { $root.sortToggle('city'); }\" class=\"\">\n                  City\n                  <i data-bind=\"css: {'fi-arrow-up': $root.sortKey() == 'city' &amp;&amp; $root.sortDirection() == 1, 'fi-arrow-down':  $root.sortKey() == 'city' &amp;&amp; $root.sortDirection() == -1 }\" class=\"\"></i>\n                </th>"}, {"Key": "<strong class=\"\">Comment: </strong>", "Field2": "<th style=\"cursor: pointer\" data-bind=\"click: function () { $root.sortToggle('start_date'); }\" class=\"\">\n                  Start Date\n                  <i data-bind=\"css: {'fi-arrow-up': $root.sortKey() == 'start_date' &amp;&amp; $root.sortDirection() == 1, 'fi-arrow-down':  $root.sortKey() == 'start_date' &amp;&amp; $root.sortDirection() == -1 }\" class=\"\"></i>\n                </th>"}, {"Key": "<strong class=\"\">Requirements: </strong>", "Field2": "<th class=\"\">Short Call</th>"}, {"Key": "<strong class=\"\">Report In: </strong>", "Field2": "<span data-bind=\"text: JOB_CLASS_DESCRIPTION\" class=\"\">SUBSTATION TECH</span>"}, {"Key": "<strong class=\"\">Worksite: </strong>", "Field2": "<span data-bind=\"text: POSITIONS_REQUESTED()\" class=\"\">2</span>"}, {"Key": "<strong class=\"\">Report To: </strong>", "Field2": "<span data-bind=\"text: POSITIONS_REQUESTED() - POSITIONS_FILLED()\" class=\"\">1</span>"}, {"Key": "<strong class=\"\">Hourly Wage: </strong>", "Field2": "<span data-bind=\"text: BOOK_DESCRIPTION\" class=\"\">SUBSTATION TECHNICIAN</span>"}, {"Key": "<strong class=\"\">Checkmark: </strong>", "Field2": "<span data-bind=\"text: JOB_DURATION\" class=\"\"></span>"}, {"Key": "<strong class=\"\">Region: </strong>", "Field2": "<span data-bind=\"text: COMMENT\" class=\"\"></span>"}, {"Key": "<strong class=\"\">Request Date: </strong>", "Field2": "<span data-bind=\"text: <PERSON><PERSON><PERSON>\" class=\"\"></span>"}, {"Key": "<strong class=\"\">Comments: </strong>", "Field2": "<span data-bind=\"text: (SKILLS() &amp;&amp; CONDITIONS()) ? ', ' : ''\" class=\"\"></span>"}, {"Key": "<strong class=\"\">Job Class: </strong>", "Field2": "<span data-bind=\"text: CONDITIONS\" class=\"\"></span>"}, {"Key": "<strong class=\"\">Positions Requested: </strong>", "Field2": "<span data-bind=\"text: REPORT_IN_TIME\" class=\"\"></span>"}, {"Key": "<strong class=\"\">Positions Available: </strong>", "Field2": "<span data-bind=\"text: WOR<PERSON>ITE_DESCRIPTION\" class=\"\">Statewide</span>"}, {"Key": "<strong class=\"\">Book: </strong>", "Field2": "<span data-bind=\"text: REPORT_TO_LOCATION_CODE\" class=\"\">Statewide</span>"}, {"Key": "<strong class=\"\">Duration: </strong>", "Field2": "<span data-bind=\"text: HOURLY_WAGE\" class=\"\">40.33</span>"}, {"Key": "<strong class=\"\">Comment: </strong>", "Field2": "<span data-bind=\"text: REJECTION_PENALTY() == 'T' ? 'Yes' : 'No'\" class=\"\">No</span>"}, {"Key": "<strong class=\"\">Requirements: </strong>", "Field2": "<span data-bind=\"text: REGION_CODE\" class=\"\"></span>"}, {"Key": "<strong class=\"\">Report In: </strong>", "Field2": "<span data-bind=\"text: REQUEST_DATE\" class=\"\">12/05/2024</span>"}, {"Key": "<strong class=\"\">Worksite: </strong>", "Field2": "<span data-bind=\"text: REQUEST_NOTE\" class=\"\">Start date: 12/16/24; 4-10s; Location: STATEWIDE, Per diem: $100, Contact: <PERSON> 404-615-0571</span>"}, {"Key": "<strong class=\"\">Report To: </strong>", "Field2": "<th colspan=\"2\" style=\"cursor: pointer; vertical-align:center;\" class=\"\">\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t<!-- ko if: $root.authenticated() && $root.jobsAvailable() && $root.booleanBidding() --><!-- /ko -->\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t<!-- ko if: $root.authenticated() && $root.jobsAvailable() && $root.numericBidding() --><!-- /ko -->\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t<span data-bind=\"text: EMPLOYER_NAME, click: function () { $root.sortToggle('employer'); }\" class=\"\">SERVICE ELECTRIC</span>\n\t\t\t\t\t\t\t\t\t<i data-bind=\"css: {'fi-arrow-up': $root.sortKey() == 'employer' &amp;&amp; $root.sortDirection() == 1, 'fi-arrow-down':  $root.sortKey() == 'employer' &amp;&amp; $root.sortDirection() == -1 }\" class=\"\"></i>\n\t\t\t\t\t\t\t\t</th>"}, {"Key": "<strong class=\"\">Hourly Wage: </strong>", "Field2": "<th style=\"cursor: pointer\" data-bind=\"click: function () { $root.sortToggle('city'); }\" class=\"\">\n                  City\n                  <i data-bind=\"css: {'fi-arrow-up': $root.sortKey() == 'city' &amp;&amp; $root.sortDirection() == 1, 'fi-arrow-down':  $root.sortKey() == 'city' &amp;&amp; $root.sortDirection() == -1 }\" class=\"\"></i>\n                </th>"}, {"Key": "<strong class=\"\">Checkmark: </strong>", "Field2": "<th style=\"cursor: pointer\" data-bind=\"click: function () { $root.sortToggle('start_date'); }\" class=\"\">\n                  Start Date\n                  <i data-bind=\"css: {'fi-arrow-up': $root.sortKey() == 'start_date' &amp;&amp; $root.sortDirection() == 1, 'fi-arrow-down':  $root.sortKey() == 'start_date' &amp;&amp; $root.sortDirection() == -1 }\" class=\"\"></i>\n                </th>"}, {"Key": "<strong class=\"\">Region: </strong>", "Field2": "<th class=\"\">Short Call</th>"}, {"Key": "<strong class=\"\">Request Date: </strong>", "Field2": "<span data-bind=\"text: JOB_CLASS_DESCRIPTION\" class=\"\">SUBSTATION TECH</span>"}, {"Key": "<strong class=\"\">Comments: </strong>", "Field2": "<span data-bind=\"text: POSITIONS_REQUESTED()\" class=\"\">1</span>"}, {"Key": "", "Field2": "<span data-bind=\"text: POSITIONS_REQUESTED() - POSITIONS_FILLED()\" class=\"\">1</span>"}, {"Key": "", "Field2": "<span data-bind=\"text: BOOK_DESCRIPTION\" class=\"\">SUBSTATION TECHNICIAN</span>"}, {"Key": "", "Field2": "<span data-bind=\"text: JOB_DURATION\" class=\"\"></span>"}, {"Key": "", "Field2": "<span data-bind=\"text: COMMENT\" class=\"\"></span>"}, {"Key": "", "Field2": "<span data-bind=\"text: <PERSON><PERSON><PERSON>\" class=\"\"></span>"}, {"Key": "", "Field2": "<span data-bind=\"text: (SKILLS() &amp;&amp; CONDITIONS()) ? ', ' : ''\" class=\"\"></span>"}, {"Key": "", "Field2": "<span data-bind=\"text: CONDITIONS\" class=\"\"></span>"}, {"Key": "", "Field2": "<span data-bind=\"text: REPORT_IN_TIME\" class=\"\"></span>"}, {"Key": "", "Field2": "<span data-bind=\"text: WOR<PERSON>ITE_DESCRIPTION\" class=\"\">Statewide</span>"}, {"Key": "", "Field2": "<span data-bind=\"text: REPORT_TO_LOCATION_CODE\" class=\"\">Statewide</span>"}, {"Key": "", "Field2": "<span data-bind=\"text: HOURLY_WAGE\" class=\"\">40.33</span>"}, {"Key": "", "Field2": "<span data-bind=\"text: REJECTION_PENALTY() == 'T' ? 'Yes' : 'No'\" class=\"\">No</span>"}, {"Key": "", "Field2": "<span data-bind=\"text: REGION_CODE\" class=\"\"></span>"}, {"Key": "", "Field2": "<span data-bind=\"text: REQUEST_DATE\" class=\"\">11/11/2024</span>"}, {"Key": "", "Field2": "<span data-bind=\"text: REQUEST_NOTE\" class=\"\">Sub. Tech. for statewide work. No per diem.,</span>"}, {"Key": "", "Field2": "<span data-bind=\"text: bidOrView\" class=\"\">Bidding</span>"}, {"Key": "", "Field2": "<span data-bind=\"text: openTime\" class=\"\">Monday, 03:00:00 PM Eastern Standard Time</span>"}, {"Key": "", "Field2": "<span data-bind=\"text: closeTime\" class=\"\">Monday at 07:30:00 AM Eastern Standard Time</span>"}, {"Key": "", "Field2": "<span data-bind=\"text: bidOrView\" class=\"\">Bidding</span>"}]
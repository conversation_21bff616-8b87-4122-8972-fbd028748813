from crawl4ai.extraction_strategy import JsonCssExtractionStrategy

class WebflowExtractor:
    def __init__(self):
        self.strategy = JsonCssExtractionStrategy({
            "name": "Webflow Jobs",
            "baseSelector": ".job-card, .job-listing, .w-dyn-item",
            "fields": [
                {"name": "employer", "selector": "[data-field='employer'], .employer, .company-name", "type": "text"},
                {"name": "start_date", "selector": "[data-field='start-date'], .start-date, .date", "type": "text"},
                {"name": "location", "selector": "[data-field='location'], .location, .job-location", "type": "text"},
                {"name": "position", "selector": "[data-field='position'], .position, .job-title", "type": "text"},
                {"name": "pay", "selector": "[data-field='pay'], .pay, .salary", "type": "text"},
                {"name": "requirements", "selector": "[data-field='requirements'], .requirements", "type": "text"},
                {"name": "positions_available", "selector": "[data-field='positions'], .positions", "type": "text"},
                {"name": "contact", "selector": "[data-field='contact'], .contact", "type": "text"},
                {"name": "conditions", "selector": "[data-field='conditions'], .conditions", "type": "text"}
            ]
        })

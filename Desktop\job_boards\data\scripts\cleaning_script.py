import json
import re

def clean_data():
    with open('v3/data/master.json', 'r') as f:
        data = json.load(f)

    cleaned_data = []
    for item in data:
        local_union = item.get('local_union', '')
        local_union = re.sub(r'\D', '', local_union)
        item['local_union'] = local_union

        classification = item.get('classification', '')
        classification = classification.lower()
        classification = classification.replace('0', 'o')
        classification = classification.replace('i', 'i')
        classification = re.sub(r'[^a-z,() ]', '', classification)
        if not classification.startswith('('):
            classification = '(' + classification
        if not classification.endswith(')'):
            classification = classification + ')'
        item['classification'] = classification
        cleaned_data.append(item)

    with open('v3/data/cleaned_master.json', 'w') as f:
        json.dump(cleaned_data, f, indent=2)

if __name__ == "__main__":
    clean_data()

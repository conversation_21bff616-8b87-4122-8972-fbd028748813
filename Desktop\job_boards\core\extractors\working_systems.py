from crawl4ai.extraction_strategy import JsonCssExtractionStrategy

class WorkingSystemsExtractor:
    def __init__(self):
        self.strategy = JsonCssExtractionStrategy({
            "name": "Working Systems Jobs",
            "baseSelector": "div.job-listing",
            "fields": [
                {"name": "ContractorName", "selector": "[data-bind*='ContractorName']", "type": "text"},
                {"name": "JobLocation", "selector": "[data-bind*='JobLocation']", "type": "text"},
                {"name": "StartDate", "selector": "[data-bind*='StartDate']", "type": "text"},
                {"name": "Classification", "selector": "[data-bind*='Classification']", "type": "text"},
                {"name": "NumberOfPositions", "selector": "[data-bind*='NumberOfPositions']", "type": "text"},
                {"name": "Comments", "selector": "[data-bind*='Comments']", "type": "text"}
            ]
        })

const puppeteer = require('puppeteer-core');
const path = require('path');
const { Firestore, FieldValue } = require('@google-cloud/firestore');
const pRetry = require('p-retry');
const winston = require('winston');

// Set the environment variable for Google Cloud credentials
process.env.GOOGLE_APPLICATION_CREDENTIALS = path.resolve(__dirname, 'X:/Journeyman_Jobs/v3/core/storage/config/firebase_config.json');

// Proxy Configuration (if needed)
const SBR_WS_ENDPOINT = `wss://brd-customer-hl_482339a5-zone-scraping222_111:<EMAIL>:9222`;

// Firebase Configuration
const firebaseConfig = {
  projectId: 'journeyman-jobs',
  keyFilename: path.resolve(__dirname, 'X:/Journeyman_Jobs/v3/core/storage/config/firebase_config.json')
};

const db = new Firestore(firebaseConfig);

// Setup structured logging
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(winston.format.timestamp(), winston.format.json()),
  transports: [new winston.transports.File({ filename: 'scraper.log' })]
});

// Function to sanitize strings for Firestore document IDs
function sanitizeString(str) {
  return str.replace(/[\/\\\.\#\$\[\]]/g, '').replace(/\s+/g, '_');
}

// Function to generate a consistent job ID
function generateJobId(localNumber, position, company) {
  return `${localNumber}-${sanitizeString(position)}-${sanitizeString(company)}`;
}

// Function to validate job data
function validateJobData(job) {
  const requiredFields = ['employer', 'jobClass', 'location'];
  return requiredFields.every(field => job[field] && typeof job[field] === 'string' && job[field].trim() !== '');
}

// Function to sanitize job data
function sanitizeJobData(job) {
  return {
    ...job,
    employer: job.employer?.trim() || 'N/A',
    jobClass: job.jobClass?.trim() || 'N/A',
    location: job.city?.trim() || 'N/A',
    wage: job.hourlyWage?.trim() || 'N/A',
    startDate: job.startDate?.trim() || 'N/A',
    description: job.comments?.trim() || 'N/A'
  };
}

async function main() {
  logger.info('Starting script for Local 77');
  console.log('Starting script...');

  let browser;
  try {
    // Connect to browser with retry
    browser = await pRetry(async () => {
      return puppeteer.connect({
        browserWSEndpoint: SBR_WS_ENDPOINT,
      });
    }, { retries: 3 });

    logger.info('Connected to Scraping Browser');
    console.log('Connected! Navigating...');

    const page = await browser.newPage();

    // Navigate with retry
    await pRetry(async () => {
      await page.goto('https://ibew77.workingsystems.com/workeropenjobs?contentonly=T', {
        waitUntil: 'networkidle0',
        timeout: 120000
      });
    }, { retries: 3 });

    logger.info('Navigated to IBEW 77 job listings page');

    // Wait for the table to load
    await pRetry(async () => {
      await page.waitForSelector('.wsiTable', { timeout: 60000 });
    }, { retries: 3 });

    logger.info('Job table loaded');

    // Click all expand buttons to show job details (fi-plus/fi-minus toggle)
    await pRetry(async () => {
      await page.evaluate(() => {
        const buttons = document.querySelectorAll('td[data-bind="click: function() { $root.toggleDetails($data); }"] i');
        buttons.forEach(button => {
          if (button.classList.contains('fi-plus')) {
            button.click(); // Click to expand job details
          }
        });
      });
    }, { retries: 3 });

    logger.info('Expanded all job details');

    // Extract job data with retry
    const jobs = await pRetry(async () => {
      return page.evaluate(() => {
        const jobRows = Array.from(document.querySelectorAll('.wsiTable tbody tr'));

        return jobRows.map(row => {
          // Get the employer name
          const employer = row.querySelector('span[data-bind="text: EMPLOYER_NAME"]');
          const city = row.querySelector('span[data-bind="text: CITY"]');
          const startDate = row.querySelector('span[data-bind="text: START_DATE"]');

          // Extract expanded job details (once clicked)
          const jobClass = row.querySelector('span[data-bind="text: JOB_CLASS_DESCRIPTION"]');
          const positionsRequested = row.querySelector('span[data-bind="text: POSITIONS_REQUESTED()"]');
          const positionsAvailable = row.querySelector('span[data-bind="text: POSITIONS_REQUESTED() - POSITIONS_FILLED()"]');
          const book = row.querySelector('span[data-bind="text: BOOK_DESCRIPTION"]');
          const worksite = row.querySelector('span[data-bind="text: WORKSITE_DESCRIPTION"]');
          const hourlyWage = row.querySelector('span[data-bind="text: HOURLY_WAGE"]');
          const reportTo = row.querySelector('span[data-bind="text: REPORT_TO_LOCATION_CODE"]');
          const requestDate = row.querySelector('span[data-bind="text: REQUEST_DATE"]');
          const comments = row.querySelector('span[data-bind="text: REQUEST_NOTE"]');

          // Return the extracted job details
          return {
            employer: employer ? employer.textContent.trim() : 'N/A',
            city: city ? city.textContent.trim() : 'N/A',
            startDate: startDate ? startDate.textContent.trim() : 'N/A',
            shortCall: row.querySelector('span[data-bind="text: SHORT_CALL() == \'T\' ? \'Yes\' : \'No\'"]') ? row.querySelector('span[data-bind="text: SHORT_CALL() == \'T\' ? \'Yes\' : \'No\'"]').textContent.trim() : 'N/A',
            jobClass: jobClass ? jobClass.textContent.trim() : '',
            positionsRequested: positionsRequested ? positionsRequested.textContent.trim() : '',
            positionsAvailable: positionsAvailable ? positionsAvailable.textContent.trim() : '',
            book: book ? book.textContent.trim() : '',
            worksite: worksite ? worksite.textContent.trim() : '',
            hourlyWage: hourlyWage ? hourlyWage.textContent.trim() : '',
            reportTo: reportTo ? reportTo.textContent.trim() : '',
            requestDate: requestDate ? requestDate.textContent.trim() : '',
            comments: comments ? comments.textContent.trim() : ''
          };
        });
      });
    }, { retries: 3 });

    logger.info(`Extracted ${jobs.length} job rows`);

    // Group jobs dynamically based on DOM structure
    const combinedJobs = [];
    for (let i = 0; i < jobs.length; i += 2) {
      // Check if we have a pair of rows (summary row and detail row)
      if (i + 1 < jobs.length) {
        const summaryRow = jobs[i];
        const detailRow = jobs[i + 1];

        // Only combine if the detail row has job class information
        if (detailRow.jobClass) {
          combinedJobs.push({
            employer: summaryRow.employer,
            city: summaryRow.city,
            startDate: summaryRow.startDate,
            shortCall: summaryRow.shortCall,
            jobClass: detailRow.jobClass,
            positionsRequested: detailRow.positionsRequested,
            positionsAvailable: detailRow.positionsAvailable,
            book: detailRow.book,
            worksite: detailRow.worksite,
            hourlyWage: detailRow.hourlyWage,
            reportTo: detailRow.reportTo,
            requestDate: detailRow.requestDate,
            comments: detailRow.comments
          });
        }
      }
    }

    logger.info(`Combined ${combinedJobs.length} job listings`);
    console.log('Combined Job Details:', combinedJobs);

    // Fix local number to 77 (was incorrectly set to 84)
    const localNumber = '77';
    logger.info(`Using local number: ${localNumber}`);

    // Fetch existing jobs from Firestore for the local number with retry
    const firestoreJobsSnapshot = await pRetry(async () => {
      return db.collection('jobs').where('localNumber', '==', localNumber).get();
    }, { retries: 3 });

    const firestoreJobsMap = new Map();
    firestoreJobsSnapshot.forEach(doc => {
      firestoreJobsMap.set(doc.id, doc.data());
    });

    logger.info(`Found ${firestoreJobsMap.size} existing jobs in Firestore`);

    // Prepare a set to keep track of current job IDs
    const currentJobIds = new Set();

    // Process combined jobs
    for (const job of combinedJobs) {
      // Sanitize job data
      const sanitizedJob = sanitizeJobData(job);

      // Validate job data
      if (!validateJobData(sanitizedJob)) {
        logger.warn(`Invalid job data for ${sanitizedJob.employer}. Skipping.`);
        continue;
      }

      const jobId = generateJobId(localNumber, sanitizedJob.jobClass, sanitizedJob.employer);
      currentJobIds.add(jobId);

      // Prepare job data according to standardized schema
      const jobData = {
        jobId: jobId,
        localNumber,
        employer: sanitizedJob.employer,
        jobClass: sanitizedJob.jobClass,
        location: sanitizedJob.location,
        wage: sanitizedJob.wage,
        startDate: sanitizedJob.startDate,
        description: sanitizedJob.description,
        timestamp: firestoreJobsMap.has(jobId) ? firestoreJobsMap.get(jobId).timestamp : FieldValue.serverTimestamp()
      };

      const jobDocRef = db.collection('jobs').doc(jobId);

      try {
        if (firestoreJobsMap.has(jobId)) {
          // Update existing job without changing the initial timestamp
          await pRetry(async () => {
            await jobDocRef.update({
              ...jobData,
              timestamp: firestoreJobsMap.get(jobId).timestamp
            });
          }, { retries: 3 });
          logger.info(`Updated job ${jobId}`);
          console.log(`Updated job ${jobId}`);
        } else {
          // Create new job with initial timestamp
          await pRetry(async () => {
            await jobDocRef.set(jobData);
          }, { retries: 3 });
          logger.info(`Added new job ${jobId}`);
          console.log(`Added new job ${jobId}`);
        }
      } catch (error) {
        logger.error(`Error updating/creating job ${jobId}: ${error.message}`);
        console.error(`Error updating/creating job ${jobId}: ${error.message}`);
      }
    }

    // Delete jobs from Firestore that are no longer on the website
    for (const [jobId, jobData] of firestoreJobsMap.entries()) {
      if (!currentJobIds.has(jobId)) {
        try {
          await pRetry(async () => {
            await db.collection('jobs').doc(jobId).delete();
          }, { retries: 3 });
          logger.info(`Deleted outdated job ${jobId}`);
          console.log(`Deleted outdated job ${jobId}`);
        } catch (error) {
          logger.error(`Error deleting job ${jobId}: ${error.message}`);
          console.error(`Error deleting job ${jobId}: ${error.message}`);
        }
      }
    }

    logger.info('Job processing completed successfully');

  } catch (err) {
    logger.error(`An error occurred: ${err.message}`, { stack: err.stack });
    console.error('An error occurred:', err);
  } finally {
    if (browser) {
      await browser.close();
      logger.info('Browser closed');
    }
  }
}

if (require.main === module) {
  main().catch(err => {
    logger.error(`Script failed: ${err.message}`, { stack: err.stack });
    console.error(err.stack || err);
    process.exit(1);
  });
}

module.exports = main;

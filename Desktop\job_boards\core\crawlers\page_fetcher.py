
from typing import Dict, List, Optional, Union
from dataclasses import dataclass
import asyncio
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>, TimeoutError
import aiofiles
import logging
from pathlib import Path
from datetime import datetime
import json
import re
from urllib.parse import urlparse


@dataclass
class FetchResult:
    url: str
    html: Optional[str]
    status_code: int
    error: Optional[str] = None
    screenshot_path: Optional[str] = None
    metadata: Dict = None

class PageFetcher:
    """Smart page fetcher with retry logic and proxy support"""
    
    def __init__(
        self,
        bright_data_auth: str,
        output_dir: Path,
        max_retries: int = 3,
        max_concurrent: int = 3,
        request_delay: float = 0.5
    ):
        self.bright_data_url = f'https://{bright_data_auth}@brd.superproxy.io:9222'
        self.output_dir = output_dir
        self.max_retries = max_retries
        self.request_delay = request_delay
        self.logger = logging.getLogger(__name__)
        
        # Concurrency control
        self.semaphore = asyncio.Semaphore(max_concurrent)
        
        # Initialize directories
        self.screenshots_dir = output_dir / 'screenshots'
        self.html_dir = output_dir / 'html'
        self.screenshots_dir.mkdir(parents=True, exist_ok=True)
        self.html_dir.mkdir(parents=True, exist_ok=True)
        
    async def init_browser(self) -> Browser:
        """Initialize Playwright browser with proxy"""
        playwright = await async_playwright().start()
        return await playwright.chromium.connect_over_cdp(self.bright_data_url)

    async def fetch_with_retry(
        self,
        url: str,
        take_screenshot: bool = True
    ) -> FetchResult:
        """Fetch a page with retry logic"""
        errors = []
        
        for attempt in range(self.max_retries):
            try:
                async with self.semaphore:
                    result = await self._fetch_page(url, take_screenshot)
                    if result.status_code == 200 and result.html:
                        return result
                        
                    errors.append(f"Attempt {attempt + 1}: Status {result.status_code}")
                    
                # Exponential backoff
                await asyncio.sleep(self.request_delay * (2 ** attempt))
                
            except Exception as e:
                errors.append(f"Attempt {attempt + 1}: {str(e)}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(self.request_delay * (2 ** attempt))
                    continue
                
        return FetchResult(
            url=url,
            html=None,
            status_code=0,
            error=f"All retries failed: {'; '.join(errors)}"
        )

    async def _fetch_page(self, url: str, take_screenshot: bool) -> FetchResult:
        """Internal method to fetch a single page"""
        browser = await self.init_browser()
        
        try:
            # Create new page
            page = await browser.new_page()
            
            # Set standard viewport
            await page.set_viewport_size({"width": 1280, "height": 1024})
            
            # Navigate with timeout
            response = await page.goto(url, wait_until='networkidle', timeout=30000)
            status_code = response.status if response else 0
            
            # Wait for content to load
            await asyncio.sleep(2)
            
            # Get HTML content
            html = await page.content()
            
            # Take screenshot if requested
            screenshot_path = None
            if take_screenshot:
                screenshot_path = self._get_screenshot_path(url)
                await page.screenshot(path=str(screenshot_path), full_page=True)
            
            # Get metadata
            metadata = {
                'title': await page.title(),
                'timestamp': datetime.now().isoformat(),
                'url': url,
                'headers': dict(response.headers) if response else {},
            }
            
            # Save HTML content
            html_path = self._get_html_path(url)
            await self._save_html(html, html_path)
            
            return FetchResult(
                url=url,
                html=html,
                status_code=status_code,
                screenshot_path=str(screenshot_path) if screenshot_path else None,
                metadata=metadata
            )
            
        except TimeoutError:
            return FetchResult(
                url=url,
                html=None,
                status_code=408,
                error="Page load timeout"
            )
            
        except Exception as e:
            return FetchResult(
                url=url,
                html=None,
                status_code=0,
                error=str(e)
            )
            
        finally:
            await browser.close()

    def _get_screenshot_path(self, url: str) -> Path:
        """Generate screenshot filename from URL"""
        domain = urlparse(url).netloc
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        return self.screenshots_dir / f"{domain}_{timestamp}.png"

    def _get_html_path(self, url: str) -> Path:
        """Generate HTML filename from URL"""
        domain = urlparse(url).netloc
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        return self.html_dir / f"{domain}_{timestamp}.html"

    async def _save_html(self, html: str, path: Path) -> None:
        """Save HTML content to file"""
        try:
            async with aiofiles.open(path, 'w', encoding='utf-8') as f:
                await f.write(html)
        except Exception as e:
            self.logger.error(f"Error saving HTML: {str(e)}")

    async def fetch_multiple(
        self,
        urls: List[str],
        take_screenshots: bool = True
    ) -> Dict[str, FetchResult]:
        """Fetch multiple pages concurrently"""
        tasks = [
            self.fetch_with_retry(url, take_screenshots)
            for url in urls
        ]
        
        results = await asyncio.gather(*tasks)
        return {result.url: result for result in results}

    async def save_results(self, results: Dict[str, FetchResult]) -> None:
        """Save fetch results metadata"""
        output = {
            url: {
                'status_code': result.status_code,
                'error': result.error,
                'screenshot': result.screenshot_path,
                'html_path': str(self._get_html_path(url)),
                'metadata': result.metadata,
                'timestamp': datetime.now().isoformat()
            }
            for url, result in results.items()
        }
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_path = self.output_dir / f'fetch_results_{timestamp}.json'
        
        with open(output_path, 'w') as f:
            json.dump(output, f, indent=2)

# Example usage
async def main():
    # Load URLs from files
    try:
        with open('data/outside_locals.json', 'r') as f:
            outside_locals = json.load(f)
            urls = [local['url'] for local in outside_locals if local['url']]
            
        # Filter out any invalid URLs
        urls = [url for url in urls if url.lower() != 'n/a' and url.strip()]
        
        print(f"Loaded {len(urls)} URLs to process")
        
        # Setup fetcher
        fetcher = PageFetcher(
            bright_data_auth='your-auth-here',
            output_dir=Path('output'),
            max_retries=3,
            max_concurrent=3
        )
        
        # Fetch pages in batches to manage resources
        batch_size = 5
        for i in range(0, len(urls), batch_size):
            batch = urls[i:i + batch_size]
            print(f"\nProcessing batch {i//batch_size + 1}")
            
            results = await fetcher.fetch_multiple(batch)
            await fetcher.save_results(results)
            
            # Print batch summary
            for url, result in results.items():
                if result.error:
                    print(f"❌ Error fetching {url}: {result.error}")
                else:
                    print(f"✅ Successfully fetched {url}")
                    
            # Small delay between batches
            await asyncio.sleep(2)
            
    except Exception as e:
        print(f"Error processing URLs: {str(e)}")

if __name__ == "__main__":
    asyncio.run(main())
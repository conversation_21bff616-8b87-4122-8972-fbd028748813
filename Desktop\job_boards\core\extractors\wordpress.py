from crawl4ai.extraction_strategy import JsonCssExtractionStrategy

class WordPressExtractor:
    def __init__(self):
        self.strategy = JsonCssExtractionStrategy({
            "name": "WordPress Jobs",
            "baseSelector": ".job_listings, .job-listing, article.job",
            "fields": [
                {"name": "employer", "selector": ".company_name, .company-title", "type": "text"},
                {"name": "location", "selector": ".location, .job-location", "type": "text"},
                {"name": "position", "selector": ".job-title, h1.entry-title", "type": "text"},
                {"name": "pay", "selector": ".job-type, .employment-type", "type": "text"},
                {"name": "requirements", "selector": ".job_description, .description", "type": "text"},
                {"name": "start_date", "selector": ".date-posted, .job-date", "type": "text"}
            ]
        })

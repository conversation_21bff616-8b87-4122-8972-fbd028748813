import json

def extract_inside_locals():
    with open('v3/data/master.json', 'r') as f:
        data = json.load(f)

    inside_locals = []
    for item in data:
        classification = item.get('classification', '')
        if 'i' in classification:
            inside_locals.append(item)

    with open('v3/data/inside_locals.json', 'w') as f:
        json.dump(inside_locals, f, indent=2)

if __name__ == "__main__":
    extract_inside_locals()

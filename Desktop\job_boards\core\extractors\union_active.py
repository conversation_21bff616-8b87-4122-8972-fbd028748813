from crawl4ai.extraction_strategy import JsonCssExtractionStrategy

class UnionActiveExtractor:
    def __init__(self):
        self.strategy = JsonCssExtractionStrategy({
            "name": "Union Active Jobs",
            "baseSelector": "table.jobs tr:not(:first-child)",
            "fields": [
                {"name": "employer", "selector": "td:nth-child(1)", "type": "text"},
                {"name": "start_date", "selector": "td:nth-child(2)", "type": "text"},
                {"name": "location", "selector": "td:nth-child(3)", "type": "text"},
                {"name": "position", "selector": "td:nth-child(4)", "type": "text"}
            ]
        })

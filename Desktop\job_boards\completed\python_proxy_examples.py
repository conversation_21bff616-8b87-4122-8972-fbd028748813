import asyncio
from playwright.async_api import async_playwright
from selenium.webdriver import Remote, ChromeOptions
from selenium.webdriver.chromium.remote_connection import ChromiumRemoteConnection
from selenium.webdriver.common.by import By

# Example 1: Using Playwright
AUTH = 'brd-customer-hl_482339a5-zone-scraping222_111:484fl5v89aaf'
SBR_WS_CDP = f'https://{AUTH}@brd.superproxy.io:9222'

async def playwright_example(pw):
    print('Connecting to Scraping Browser...')
    browser = await pw.chromium.connect_over_cdp(SBR_WS_CDP)
    try:
        page = await browser.new_page()
        print('Connected! Navigating to webpage')
        await page.goto('https://www.example.com')
        await page.screenshot(path="page.png", full_page=True)
        print("Screenshot saved as 'page.png'")
        html = await page.content()
        print(html)
    finally:
        await browser.close()

async def run_playwright():
    async with async_playwright() as playwright:
        await playwright_example(playwright)

# Example 2: Using Selenium
def selenium_example():
    print('Connecting to Scraping Browser...')
    SBR_WEBDRIVER = f'https://{AUTH}@brd.superproxy.io:9515'
    sbr_connection = ChromiumRemoteConnection(SBR_WEBDRIVER, 'goog', 'chrome')
    
    with Remote(sbr_connection, options=ChromeOptions()) as driver:
        print('Connected! Navigating...')
        driver.get('https://example.com')
        print('Taking page screenshot to file page.png')
        driver.get_screenshot_as_file('./page.png')
        print('Navigated! Scraping page content...')
        html = driver.page_source
        print(html)

if __name__ == '__main__':
    print("Running Playwright Example:")
    asyncio.run(run_playwright())
    
    print("\nRunning Selenium Example:")
    selenium_example()

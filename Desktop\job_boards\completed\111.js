const puppeteer = require('puppeteer-core');
const fs = require('fs').promises;
const path = require('path');
const { Firestore, FieldValue } = require('@google-cloud/firestore');
const pRetry = require('p-retry');
const winston = require('winston');

// Set the environment variable for Google Cloud credentials
process.env.GOOGLE_APPLICATION_CREDENTIALS = path.resolve(__dirname, '../core/storage/config/firebase_config.json');

// Firestore initialization
const firebaseConfig = {
  projectId: 'journeyman-jobs',
  keyFilename: path.resolve(__dirname, '../core/storage/config/firebase_config.json')
};

const db = new Firestore(firebaseConfig);

// Setup structured logging
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(winston.format.timestamp(), winston.format.json()),
  transports: [new winston.transports.File({ filename: 'scraper.log' })]
});

// Proxy Configuration
const AUTH = 'brd-customer-hl_482339a5-zone-scraping222_111:484fl5v89aaf';
const SBR_WS_ENDPOINT = `wss://${AUTH}@brd.superproxy.io:9222`;

// Adjust this variable to match the specific local you are targeting
const localNumber = '111';

// Function to sanitize strings for Firestore document IDs
function sanitizeString(str) {
    return str.replace(/[\/\\\.\#\$\[\]]/g, '').replace(/\s+/g, '_');
}

// Function to generate a consistent job ID
function generateJobId(localNumber, classification, job) {
    const company = sanitizeString(job.company || '');
    const location = sanitizeString(job.location || '');
    return `${localNumber}-${classification}-${company}-${location}`;
}

// Function to validate job data
function validateJobData(job) {
    const requiredFields = ['employer', 'jobClass', 'location'];
    return requiredFields.every(field => job[field] && typeof job[field] === 'string' && job[field].trim() !== '');
}

// Function to sanitize job data
function sanitizeJobData(job) {
    return {
        ...job,
        employer: job.company?.trim() || 'N/A',
        jobClass: job.jobClass?.trim() || 'N/A',
        location: job.location?.trim() || 'N/A',
        wage: job.wage?.trim() || 'N/A',
        startDate: job.startDate?.trim() || 'N/A',
        description: job.qualifications?.trim() || 'N/A'
    };
}

async function scrapeJobsForClassification(page, classification) {
    logger.info(`Scraping data for ${classification}...`);
    console.log(`Scraping data for ${classification}...`);

    try {
        const jobs = await pRetry(async () => {
            return page.evaluate((classification) => {
                const jobElements = document.querySelectorAll(`div.${classification}.dispatch-section div.dispatch_cms-item`);
                const jobs = [];

                jobElements.forEach(job => {
                    const company = job.querySelector('.company .dispatch-paragraph')?.innerText.trim();
                    const datePosted = job.querySelector('.dateposted .dispatch-paragraph')?.innerText.trim();
                    const jobClass = job.querySelector('.class .dispatch-paragraph')?.innerText.trim();
                    const numberOfJobs = job.querySelector('.ofjobs .dispatch-paragraph')?.innerText.trim();
                    const location = job.querySelector('.locations .dispatch-paragraph p')?.innerText.trim();
                    const hours = job.querySelector('.hours .dispatch-paragraph')?.innerText.trim();
                    const startDate = job.querySelector('.startdate .dispatch-paragraph')?.innerText.trim();
                    const wage = job.querySelector('.wage .dispatch-paragraph')?.innerText.trim();
                    const startTime = job.querySelector('.starttime .dispatch-paragraph')?.innerText.trim();
                    const sub = job.querySelector('.sub .dispatch-paragraph')?.innerText.trim();
                    const qualifications = job.querySelector('.qual .dispatch-paragraph p')?.innerText.trim();
                    const agreement = job.querySelector('.agreement .dispatch-paragraph')?.innerText.trim();

                    jobs.push({
                        company,
                        datePosted,
                        jobClass,
                        numberOfJobs,
                        location,
                        hours,
                        startDate,
                        wage,
                        startTime,
                        sub,
                        qualifications,
                        agreement,
                    });
                });

                return jobs;
            }, classification);
        }, { retries: 3 });

        logger.info(`Successfully scraped ${jobs.length} jobs for ${classification}`);
        console.log(`Finished scraping data for ${classification}.`);
        return jobs;
    } catch (error) {
        logger.error(`Failed to scrape jobs for ${classification}: ${error.message}`, { stack: error.stack });
        console.error(`Failed to scrape jobs for ${classification}: ${error.message}`);
        throw error;
    }
}

async function updateDatabaseWithJobs(classification, jobs) {
    try {
        logger.info(`Updating database with ${jobs.length} jobs for classification ${classification}`);

        // Fetch existing jobs from Firestore for the specific local and classification
        const existingJobsSnapshot = await pRetry(async () => {
            return db
                .collection('jobs')
                .where('localNumber', '==', localNumber)
                .where('classification', '==', classification)
                .get();
        }, { retries: 3 });

        const existingJobs = new Map();
        existingJobsSnapshot.forEach(doc => {
            existingJobs.set(doc.id, doc.data());
        });
        logger.info(`Found ${existingJobs.size} existing jobs in Firestore for classification ${classification}`);

        // Track job IDs that are still valid
        const validJobIds = new Set();

        for (const job of jobs) {
            // Sanitize job data to match standardized schema
            const sanitizedJob = sanitizeJobData(job);

            // Create a unique, sanitized job ID
            const jobId = generateJobId(localNumber, classification, job);
            validJobIds.add(jobId);

            // Prepare job data according to standardized schema
            const jobData = {
                jobId: jobId,
                localNumber,
                employer: sanitizedJob.employer,
                jobClass: sanitizedJob.jobClass || classification.replace(/-/g, ' '),
                location: sanitizedJob.location,
                wage: sanitizedJob.wage,
                startDate: sanitizedJob.startDate,
                description: sanitizedJob.description,
                timestamp: existingJobs.has(jobId) ? existingJobs.get(jobId).timestamp : FieldValue.serverTimestamp()
            };

            // Validate job data
            if (!validateJobData(jobData)) {
                logger.warn(`Invalid job data for ${jobId}. Skipping.`);
                continue;
            }

            const jobDocRef = db.collection('jobs').doc(jobId);

            try {
                if (existingJobs.has(jobId)) {
                    // Job exists; update it without changing the initial timestamp
                    await pRetry(async () => {
                        await jobDocRef.update({
                            ...jobData,
                            timestamp: existingJobs.get(jobId).timestamp || FieldValue.serverTimestamp(),
                        });
                    }, { retries: 3 });
                    logger.info(`Updated job ${jobId}`);
                    console.log(`Updated job ${jobId}`);
                } else {
                    // Job doesn't exist; create it with a timestamp
                    await pRetry(async () => {
                        await jobDocRef.set(jobData);
                    }, { retries: 3 });
                    logger.info(`Added new job ${jobId}`);
                    console.log(`Added new job ${jobId}`);
                }
            } catch (error) {
                logger.error(`Error updating/creating job ${jobId}: ${error.message}`);
                console.error(`Error updating/creating job ${jobId}: ${error.message}`);
            }
        }

        // Delete jobs that are no longer on the website
        for (const [jobId] of existingJobs) {
            if (!validJobIds.has(jobId)) {
                try {
                    await pRetry(async () => {
                        await db.collection('jobs').doc(jobId).delete();
                    }, { retries: 3 });
                    logger.info(`Deleted outdated job ${jobId}`);
                    console.log(`Deleted outdated job ${jobId}`);
                } catch (error) {
                    logger.error(`Error deleting job ${jobId}: ${error.message}`);
                    console.error(`Error deleting job ${jobId}: ${error.message}`);
                }
            }
        }

        logger.info(`Database update completed for classification ${classification}`);
    } catch (error) {
        logger.error(`Failed to update database for classification ${classification}: ${error.message}`, { stack: error.stack });
        console.error(`Failed to update database for classification ${classification}: ${error.message}`);
        throw error;
    }
}

async function main() {
    logger.info('Starting script for Local 111');
    console.log('Connecting to Scraping Browser...');

    let browser;
    let page;

    try {
        // Connect to browser with retry
        browser = await pRetry(async () => {
            return puppeteer.connect({
                browserWSEndpoint: SBR_WS_ENDPOINT,
            });
        }, { retries: 3 });

        logger.info('Connected to Scraping Browser');
        console.log('Connected! Navigating...');

        page = await browser.newPage();

        // Navigate with retry
        await pRetry(async () => {
            await page.goto('https://www.ibew111.org/dispatch', {
                waitUntil: 'networkidle0',
                timeout: 2 * 60 * 1000
            });
        }, { retries: 3 });

        logger.info('Navigated to IBEW 111 dispatch page');
        console.log('Navigated! Scraping page content...');

        const classifications = [
            'journeyman-lineman',
            'journeyman-wireman',
            'traffic-technician',
            'journeyman-fitter',
            'street-light-technician',
            'operator',
            'groundman',
            'cdl-groundman',
            'line-equipment-operator',
        ];

        for (const classification of classifications) {
            try {
                await pRetry(async () => {
                    const jobs = await scrapeJobsForClassification(page, classification);
                    await updateDatabaseWithJobs(classification, jobs);
                }, { retries: 3 });

                // Delay before moving to the next classification to avoid overwhelming the server
                await new Promise(resolve => setTimeout(resolve, 3000)); // 3-second delay
            } catch (error) {
                logger.error(`Failed to process classification ${classification}: ${error.message}`);
                console.error(`Failed to process classification ${classification}: ${error.message}`);
                // Continue with next classification even if this one fails
            }
        }

        logger.info('All job data has been updated in Firestore');
        console.log('All job data has been updated in Firestore.');
    } catch (err) {
        logger.error(`An error occurred: ${err.message}`, { stack: err.stack });
        console.error('An error occurred:', err);
    } finally {
        try {
            if (page) {
                logger.info('Taking screenshot');
                console.log('Taking screenshot to page.png');
                const screenshotPath = path.join(__dirname, 'page.png');
                await fs.mkdir(path.dirname(screenshotPath), { recursive: true });
                await page.screenshot({ path: screenshotPath, fullPage: true });
            }
        } catch (error) {
            logger.error(`Error taking screenshot: ${error.message}`);
            console.error('Error taking screenshot:', error);
        }

        if (browser) {
            await browser.close();
            logger.info('Browser closed');
        }
    }
}

if (require.main === module) {
    main().catch(err => {
        logger.error(`Script failed: ${err.message}`, { stack: err.stack });
        console.error(err.stack || err);
        process.exit(1);
    });
}

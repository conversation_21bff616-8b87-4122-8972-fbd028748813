const puppeteer = require('puppeteer-core');
const fs = require('fs').promises;
const path = require('path');
const { Firestore, FieldValue } = require('@google-cloud/firestore');
const pRetry = require('p-retry');
const winston = require('winston');

// Set the environment variable for Google Cloud credentials
process.env.GOOGLE_APPLICATION_CREDENTIALS = path.resolve(__dirname, '../core/storage/config/firebase_config.json');

// Firestore initialization
const firebaseConfig = {
  projectId: 'journeyman-jobs',
  keyFilename: path.resolve(__dirname, '../core/storage/config/firebase_config.json')
};

const db = new Firestore(firebaseConfig);

// Setup structured logging
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(winston.format.timestamp(), winston.format.json()),
  transports: [new winston.transports.File({ filename: 'scraper.log' })]
});

// Proxy Configuration
const AUTH = 'brd-customer-hl_482339a5-zone-scraping222_111:484fl5v89aaf';
const SBR_WS_ENDPOINT = `wss://${AUTH}@brd.superproxy.io:9222`;

// Adjust this variable to match the specific local you are targeting
const localNumber = '1249';

// Function to sanitize strings for Firestore document IDs
function sanitizeString(str) {
  return str ? str.replace(/[\/\\.\#\$\[\]]/g, '').replace(/\s+/g, '_') : '';
}

// Function to generate a consistent job ID
function generateJobId(localNumber, classification, job) {
  const company = sanitizeString(job.Company || '');
  const location = sanitizeString(job.Location || '');
  return `${localNumber}-${classification}-${company}-${location}`;
}

// Function to validate job data
function validateJobData(job) {
  const requiredFields = ['employer', 'jobClass', 'location'];
  return requiredFields.every(field => job[field] && typeof job[field] === 'string' && job[field].trim() !== '');
}

// Function to sanitize job data
function sanitizeJobData(job) {
  return {
    ...job,
    employer: job.Company?.trim() || 'N/A',
    jobClass: job['Type of Work']?.trim() || 'N/A',
    location: job.Location?.trim() || 'N/A',
    wage: job.Benefits?.trim() || 'N/A',
    startDate: 'N/A',
    description: job.Notes?.trim() || 'N/A'
  };
}

async function scrapeJobPostings() {
  logger.info('Starting job scraping for Local 1249');
  console.log('Starting job scraping for Local 1249...');

  let browser;
  try {
    // URL of the job board page
    const targetURL = 'https://ibew1249.org/job-board/';

    // Launch Puppeteer to extract the iframe src with retry
    browser = await pRetry(async () => {
      return puppeteer.connect({
        browserWSEndpoint: SBR_WS_ENDPOINT,
      });
    }, { retries: 3 });

    logger.info('Connected to Scraping Browser');

    const page = await browser.newPage();

    // Navigate with retry
    await pRetry(async () => {
      await page.goto(targetURL, {
        waitUntil: 'networkidle2',
        timeout: 2 * 60 * 1000
      });
    }, { retries: 3 });

    logger.info('Navigated to IBEW 1249 job board page');

    // Extract the iframe src with retry
    const iframeSrc = await pRetry(async () => {
      return page.evaluate(() => {
        const iframe = document.querySelector('iframe[src*="docs.google.com/spreadsheets"]');
        return iframe ? iframe.src : null;
      });
    }, { retries: 3 });

    await browser.close();
    logger.info('Browser closed');

    if (!iframeSrc) {
      const error = new Error('Google Sheets iframe not found on the page.');
      logger.error(error.message);
      throw error;
    }

    logger.info(`Found iframe source: ${iframeSrc}`);
    console.log(`Found iframe source: ${iframeSrc}`);

    // Extract the Spreadsheet ID from the iframe src
    const spreadsheetIdMatch = iframeSrc.match(/\/d\/([a-zA-Z0-9-_]+)/);
    if (!spreadsheetIdMatch || spreadsheetIdMatch.length < 2) {
      const error = new Error('Unable to extract Spreadsheet ID from iframe src.');
      logger.error(error.message);
      throw error;
    }
    const spreadsheetId = spreadsheetIdMatch[1];

    logger.info(`Extracted Spreadsheet ID: ${spreadsheetId}`);
    console.log(`Extracted Spreadsheet ID: ${spreadsheetId}`);

    // Construct the CSV export URL
    const csvExportUrl = `https://docs.google.com/spreadsheets/d/${spreadsheetId}/export?format=csv`;

    logger.info(`CSV Export URL: ${csvExportUrl}`);
    console.log(`CSV Export URL: ${csvExportUrl}`);

    // Fetch the CSV data using the built-in fetch with retry
    const csvData = await pRetry(async () => {
      const response = await fetch(csvExportUrl);
      if (!response.ok) {
        throw new Error(`Failed to download CSV data: ${response.statusText}`);
      }
      return response.text();
    }, { retries: 3 });

    logger.info('CSV data downloaded successfully');
    console.log('CSV data downloaded successfully.');

    // Parse the CSV data
    const rows = csvData.split('\n').map(row => {
      // Handle quoted fields properly
      const fields = [];
      let field = '';
      let inQuotes = false;

      for (let i = 0; i < row.length; i++) {
        if (row[i] === '"') {
          inQuotes = !inQuotes;
        } else if (row[i] === ',' && !inQuotes) {
          fields.push(field);
          field = '';
        } else {
          field += row[i];
        }
      }
      fields.push(field);
      return fields;
    });

    logger.info(`Parsed ${rows.length} rows from CSV`);
    console.log('First few rows:', rows.slice(0, 5));

    const filteredJobPostings = [];

    // Make header mapping dynamic
    const headers = rows[0].map(h => h.trim().replace(/^["'\s]+|["'\s]+$/g, ''));
    logger.info(`Found headers: ${headers.join(', ')}`);

    // Process each row
    for (let i = 1; i < rows.length; i++) {
      const row = rows[i];

      // Skip empty rows or rows that don't have enough columns
      if (!row[0] || row.length < 3) continue;

      // Skip rows that don't look like job entries
      if (row[0].toLowerCase().includes('job') ||
          row[0].toLowerCase().includes('company') ||
          row[0].trim() === '') {
        continue;
      }

      // Create job object from row
      const jobData = {};
      headers.forEach((header, index) => {
        let value = row[index] ? row[index].trim() : '';
        // Remove any quotes and extra whitespace
        value = value.replace(/^["'\s]+|["'\s]+$/g, '');

        if (header === "Company") {
          // Clean up company name if it has a number prefix
          const match = value.match(/^\d+\s*(.+)$/);
          jobData[header] = match ? match[1].trim() : value;
        } else {
          jobData[header] = value || 'N/A';
        }
      });

      if (jobData.Company && jobData.Company !== '') {
        filteredJobPostings.push(jobData);
      }
    }

    logger.info(`Parsed ${filteredJobPostings.length} job postings`);
    console.log(`Parsed ${filteredJobPostings.length} job postings.`);
    if (filteredJobPostings.length > 0) {
      console.log('First job posting:', filteredJobPostings[0]);
    }

    // Fetch existing jobs from Firestore for local 1249 with retry
    const firestoreJobsSnapshot = await pRetry(async () => {
      return db.collection('jobs')
        .where('localNumber', '==', localNumber)
        .get();
    }, { retries: 3 });

    const firestoreJobsMap = new Map();
    firestoreJobsSnapshot.forEach((doc) => {
      firestoreJobsMap.set(doc.id, doc.data());
    });

    logger.info(`Found ${firestoreJobsMap.size} existing jobs in Firestore`);

    // Prepare a set to keep track of current job IDs from the spreadsheet
    const currentJobIds = new Set();

    // Process and store each job posting in Firestore
    for (const job of filteredJobPostings) {
      // Sanitize job data
      const sanitizedJob = sanitizeJobData(job);

      // Extract classification from Type of Work or use default
      const classification = job['Type of Work']?.trim() || 'Journeyman_Lineman';

      // Create a unique, sanitized job ID
      const jobId = generateJobId(localNumber, classification, job);
      currentJobIds.add(jobId);

      // Prepare job data according to standardized schema
      const jobData = {
        jobId: jobId,
        localNumber,
        employer: sanitizedJob.employer,
        jobClass: sanitizedJob.jobClass,
        location: sanitizedJob.location,
        wage: sanitizedJob.wage,
        startDate: sanitizedJob.startDate,
        description: sanitizedJob.description,
        timestamp: firestoreJobsMap.has(jobId) ? firestoreJobsMap.get(jobId).timestamp : FieldValue.serverTimestamp()
      };

      // Validate job data
      if (!validateJobData(jobData)) {
        logger.warn(`Invalid job data for ${jobId}. Skipping.`);
        continue;
      }

      const jobDocRef = db.collection('jobs').doc(jobId);

      try {
        if (firestoreJobsMap.has(jobId)) {
          // Job exists; update it without changing the initial timestamp
          await pRetry(async () => {
            await jobDocRef.update({
              ...jobData,
              timestamp: firestoreJobsMap.get(jobId).timestamp,
            });
          }, { retries: 3 });
          logger.info(`Updated job ${jobId}`);
          console.log(`Updated job ${jobId}`);
        } else {
          // Job doesn't exist; create it with a timestamp
          await pRetry(async () => {
            await jobDocRef.set(jobData);
          }, { retries: 3 });
          logger.info(`Added new job ${jobId}`);
          console.log(`Added new job ${jobId}`);
        }
      } catch (error) {
        logger.error(`Error updating/creating job ${jobId}: ${error.message}`);
        console.error(`Error updating/creating job ${jobId}: ${error.message}`);
      }
    }

    // Delete jobs from Firestore that are no longer in the spreadsheet
    for (const [jobId] of firestoreJobsMap.entries()) {
      if (!currentJobIds.has(jobId)) {
        try {
          await pRetry(async () => {
            await db.collection('jobs').doc(jobId).delete();
          }, { retries: 3 });
          logger.info(`Deleted outdated job ${jobId}`);
          console.log(`Deleted outdated job ${jobId}`);
        } catch (error) {
          logger.error(`Error deleting job ${jobId}: ${error.message}`);
          console.error(`Error deleting job ${jobId}: ${error.message}`);
        }
      }
    }

    logger.info('Scraping and data storage completed successfully');
    console.log('Scraping and data storage completed successfully.');
  } catch (err) {
    logger.error(`An error occurred during scraping: ${err.message}`, { stack: err.stack });
    console.error('An error occurred during scraping:', err);
    throw err;
  } finally {
    if (browser) {
      try {
        await browser.close();
        logger.info('Browser closed');
      } catch (error) {
        logger.error(`Error closing browser: ${error.message}`);
      }
    }
  }
}

// Execute the scraping function if the script is run directly
if (require.main === module) {
  scrapeJobPostings().catch((err) => {
    logger.error(`Script failed: ${err.message}`, { stack: err.stack });
    console.error('Unhandled error:', err);
    process.exit(1);
  });
}
# core/crawling/config.py

import os
from pathlib import Path
from dotenv import load_dotenv

# Load .env file if it exists
load_dotenv()

# Crawler Configuration
CRAWLER_CONFIG = {
    'bright_data': {
        'username': os.getenv('BRIGHT_DATA_USERNAME'),
        'password': os.getenv('BRIGHT_DATA_PASSWORD'),
        'auth': os.getenv('BRIGHT_DATA_AUTH'),  # or construct it from username/password
        'proxy_host': 'brd.superproxy.io',
        'proxy_port': '9222'
    },
    'requests': {
        'max_concurrent': 3,
        'request_delay': 0.5,
        'max_retries': 3
    }
}
import asyncio
import logging
from playwright.async_api import async_playwright
from tenacity import retry, stop_after_attempt, wait_exponential
from google.cloud import firestore

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    filename='scraper.log'
)
logger = logging.getLogger('playwright111')

# Proxy Configuration
AUTH = 'brd-customer-hl_482339a5-zone-scraping222_111:484fl5v89aaf'
SBR_WS_CDP = f'wss://{AUTH}@brd.superproxy.io:9222'

# Local number
LOCAL_NUMBER = '111'

# Retry decorator for network operations
@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
async def navigate(page, url):
    await page.goto(url, wait_until='networkidle', timeout=120000)

@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
async def scrape_jobs(page):
    await page.wait_for_selector('div.dispatch_cms-item')
    jobs = await page.evaluate('''
        () => {
            const jobElements = document.querySelectorAll('div.journeyman-lineman.dispatch-section div.dispatch_cms-item');
            return Array.from(jobElements).map(job => ({
                company: job.querySelector('.company .dispatch-paragraph')?.innerText.trim() || 'N/A',
                jobClass: job.querySelector('.class .dispatch-paragraph')?.innerText.trim() || 'N/A',
                location: job.querySelector('.locations .dispatch-paragraph p')?.innerText.trim() || 'N/A',
                wage: job.querySelector('.wage .dispatch-paragraph')?.innerText.trim() || 'N/A',
                startDate: job.querySelector('.startdate .dispatch-paragraph')?.innerText.trim() || 'N/A',
                description: job.querySelector('.qual .dispatch-paragraph p')?.innerText.trim() || 'N/A'
            }));
        }
    ''')
    return jobs

def validate_job_data(job):
    required_fields = ['company', 'jobClass', 'location']
    return all(job.get(field) and isinstance(job[field], str) and job[field].strip() for field in required_fields)

def sanitize_string(s):
    if not s:
        return ''
    return s.replace('/', '_').replace('\\', '_').replace('.', '_').replace('#', '_').replace('$', '_').replace('[', '_').replace(']', '_').replace(' ', '_')

def generate_job_id(local_number, job_class, company):
    sanitized_job_class = sanitize_string(job_class)
    sanitized_company = sanitize_string(company)
    return f"{local_number}-{sanitized_job_class}-{sanitized_company}"

@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
async def update_firestore(jobs):
    db = firestore.AsyncClient(project='journeyman-jobs')
    current_job_ids = set()

    # Get existing jobs
    query = db.collection('jobs').where('localNumber', '==', LOCAL_NUMBER)
    existing_jobs = {}
    async for doc in query.stream():
        existing_jobs[doc.id] = doc.to_dict()

    logger.info(f"Found {len(existing_jobs)} existing jobs in Firestore")

    # Update or add jobs
    for job in jobs:
        if not validate_job_data(job):
            logger.warning(f"Invalid job data: {job}. Skipping.")
            continue

        job_id = generate_job_id(LOCAL_NUMBER, job['jobClass'], job['company'])
        current_job_ids.add(job_id)

        job_data = {
            'jobId': job_id,
            'localNumber': LOCAL_NUMBER,
            'employer': job['company'],
            'jobClass': job['jobClass'],
            'location': job['location'],
            'wage': job['wage'],
            'startDate': job['startDate'],
            'description': job['description'],
        }

        doc_ref = db.collection('jobs').document(job_id)

        if job_id in existing_jobs:
            # Preserve the original timestamp
            job_data['timestamp'] = existing_jobs[job_id].get('timestamp')
            await doc_ref.update(job_data)
            logger.info(f"Updated job {job_id}")
        else:
            # Set server timestamp for new jobs
            job_data['timestamp'] = firestore.SERVER_TIMESTAMP
            await doc_ref.set(job_data)
            logger.info(f"Added new job {job_id}")

    # Delete outdated jobs
    for job_id in existing_jobs:
        if job_id not in current_job_ids:
            await db.collection('jobs').document(job_id).delete()
            logger.info(f"Deleted outdated job {job_id}")

async def run(pw):
    logger.info('Starting script for Local 111')
    print('Connecting to Scraping Browser...')

    browser = await pw.chromium.connect_over_cdp(SBR_WS_CDP)
    try:
        page = await browser.new_page()
        logger.info('Connected! Navigating to IBEW 111 dispatch page')
        print('Connected! Navigating to webpage')

        await navigate(page, 'https://www.ibew111.org/dispatch')
        logger.info('Successfully navigated to IBEW 111 dispatch page')

        # Scrape jobs
        jobs = await scrape_jobs(page)
        logger.info(f'Successfully scraped {len(jobs)} jobs')
        print(f'Scraped {len(jobs)} jobs')

        # Update Firestore
        await update_firestore(jobs)
        logger.info('Firestore update completed')
        print('Firestore update completed')

        # Take screenshot for verification
        await page.screenshot(path="page_screenshot.png", full_page=True)
        logger.info("Screenshot saved as 'page_screenshot.png'")
        print("Screenshot saved as 'page_screenshot.png'")

    except Exception as e:
        logger.error(f"Error during execution: {str(e)}", exc_info=True)
        print(f"Error: {str(e)}")
        raise
    finally:
        await browser.close()
        logger.info('Browser closed')

async def main():
    async with async_playwright() as playwright:
        await run(playwright)

if __name__ == '__main__':
    try:
        asyncio.run(main())
    except Exception as e:
        logger.error(f"Script failed: {str(e)}", exc_info=True)
        print(f"Script failed: {str(e)}")
        exit(1)
# Plan of Correction for Journeyman Jobs Scraping Scripts

This document outlines the issues identified in the scraping scripts (`playwright111.py`, `77.js`, `84.js`, `111.js`, `125.js`, `125_copy.js`, `226.py`, `602.js`, `1249.js`) for the **Journeyman Jobs** app and provides detailed instructions for correcting them. The goal is to ensure the scripts accurately scrape job postings from IBEW local websites, format data consistently, and store it reliably in Firestore for use in the app. The corrections address issues in proxy configuration, error handling, data validation, Firestore schema consistency, and site-specific logic.

## Standardized Firestore Schema

To ensure consistency across all scripts, adopt the following Firestore schema for job documents:

```json
{
  "localNumber": "string", // e.g., "125"
  "jobId": "string", // e.g., "125-Journeyman_Lineman-Company_Name"
  "employer": "string", // e.g., "Acme Electric"
  "jobClass": "string", // e.g., "Journeyman Lineman"
  "location": "string", // e.g., "Portland, OR"
  "wage": "string", // e.g., "$45.00/hr"
  "startDate": "string", // e.g., "2025-05-01"
  "description": "string", // e.g., "Install and maintain electrical systems..."
  "timestamp": "timestamp" // Firestore server timestamp
}
```

All scripts must store jobs with these fields, using `N/A` for missing values where necessary.

## General Issues and Corrections

### 1. Inconsistent Proxy Configuration

**Issue**: Some scripts (`111.js`, `1249.js`) use `https://` for the Bright Data Scraping Browser endpoint, while others correctly use `wss://`. This can cause connection failures.

**Correction**:

- Update the `SBR_WS_ENDPOINT` in all scripts to use the WebSocket protocol (`wss://`).
- Standardize the endpoint format:

  ```javascript
  const SBR_WS_ENDPOINT = `wss://brd-customer-hl_482339a5-zone-scraping222_111:<EMAIL>:9222`;
  ```

  or for Python:

  ```python
  SBR_WS_CDP = 'wss://brd-customer-hl_482339a5-zone-scraping222_111:<EMAIL>:9222'
  ```

- Apply to:
  - `111.js`: Change `https://` to `wss://` in `SBR_WS_ENDPOINT`.
  - `1249.js`: Change `https://` to `wss://` in `SBR_WS_ENDPOINT`.

### 2. Insufficient Error Handling and Retries

**Issue**: Most scripts lack robust retry logic for network failures, timeouts, or dynamic content loading issues, risking incomplete data or script crashes.

**Correction**:

- Implement retry logic using a library:
  - For Node.js scripts (`77.js`, `84.js`, `111: Use`p-retry`(`npm install p-retry`).
  - For Python scripts (`playwright111.py`, `226.py`): Use `tenacity` (`pip install tenacity`).
- Wrap critical operations (e.g., `page.goto`, `page.evaluate`, Firestore writes) with retries:

  ```javascript
  const pRetry = require('p-retry');
  await pRetry(() => page.goto(url, { waitUntil: 'networkidle0', timeout: 120000 }), { retries: 3 });
  ```

  ```python
  from tenacity import retry, stop_after_attempt, wait_exponential
  @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
  async def navigate(page, url):
      await page.goto(url, waitUntil='networkidle0', timeout=120000)
  ```

- Log retry attempts and failures using a structured logging library:
  - Node.js: Use `winston` (`npm install winston`).
  - Python: Use the built-in `logging` module.

  ```javascript
  const winston = require('winston');
  const logger = winston.createLogger({
    level: 'info',
    format: winston.format.combine(winston.format.timestamp(), winston.format.json()),
    transports: [new winston.transports.File({ filename: 'scraper.log' })]
  });
  ```

- Apply to all scripts for navigation, element selection, and Firestore operations.

### 3. Inadequate Data Validation and Sanitization

**Issue**: Scripts do not validate extracted fields, risking incomplete or malformed data in Firestore. Fields are not consistently sanitized beyond `generateJobId`.

**Correction**:

- Implement a `validateJobData` function to check for required fields (`employer`, `jobClass`, `location`) and ensure they are non-empty strings:

  ```javascript
  function validateJobData(job) {
    const requiredFields = ['employer', 'jobClass', 'location'];
    return requiredFields.every(field => job[field] && typeof job[field] === 'string' && job[field].trim() !== '');
  }
  ```

  ```python
  def validate_job_data(job):
      required_fields = ['employer', 'job_class', 'location']
      return all(job.get(field) and isinstance(job[field], str) and job[field].strip() for field in required_fields)
  ```

- Sanitize all fields before storing:
  - Trim whitespace.
  - Replace missing values with `'N/A'`.
  - Normalize dates to ISO format (e.g., `2025-05-01`).

  ```javascript
  function sanitizeJobData(job) {
    return {
      ...job,
      employer: job.employer?.trim() || 'N/A',
      jobClass: job.jobClass?.trim() || 'N/A',
      location: job.location?.trim() || 'N/A',
      wage: job.wage?.trim() || 'N/A',
      startDate: job.startDate?.trim() || 'N/A',
      description: job.description?.trim() || 'N/A'
    };
  }
  ```

- Apply to all scripts before Firestore operations.

### 4. Inconsistent Firestore Schema

**Issue**: Scripts store different fields, leading to an inconsistent Firestore schema (e.g., `125.js` stores `title`, while `111.js` stores `wage`).

**Correction**:

- Update all scripts to store jobs using the standardized schema (see above).
- Modify the Firestore update logic to include all required fields:

  ```javascript
  const jobData = {
    jobId: generateJobId(localNumber, job.jobClass, job.employer),
    localNumber,
    employer: job.employer,
    jobClass: job.jobClass,
    location: job.location,
    wage: job.wage,
    startDate: job.startDate,
    description: job.description,
    timestamp: firestoreJobsMap.has(jobId) ? firestoreJobsMap.get(jobId).timestamp : FieldValue.serverTimestamp()
  };
  ```

- Apply to `77.js`, `84.js`, `111.js`, `125.js`, `226.py`, `602.js`, `1249.js`.

### 5. Hardcoded Assumptions

**Issue**: Scripts like `77.js`, `84.js`, `602.js` hardcode row pairings (e.g., `jobs[0]` with `jobs[1]`), and `1249.js` assumes a single classification (`Journeyman_Lineman`).

**Correction**:

- Replace hardcoded pairings with dynamic parsing:
  - For `77.js`, `84.js`, `602.js`: Group rows based on DOM structure (e.g., check if rows share a parent or unique identifier).

  ```javascript
  const jobs = await page.evaluate(() => {
    const jobRows = Array.from(document.querySelectorAll('.wsiTable tbody tr'));
    const groupedJobs = [];
    for (let i = 0; i < jobRows.length; i += 2) {
      const summaryRow = jobRows[i];
      const detailRow = jobRows[i + 1];
      if (!detailRow) continue;
      const job = {
        employer: summaryRow.querySelector('span[data-bind="text: EMPLOYER_NAME"]')?.textContent.trim() || 'N/A',
        jobClass: detailRow.querySelector('span[data-bind="text: JOB_CLASS_DESCRIPTION"]')?.textContent.trim() || 'N/A',
        // ... other fields
      };
      groupedJobs.push(job);
    }
    return groupedJobs;
  });
  ```

- For `1249.js`: Extract classification from the `Type of Work` CSV column or infer it dynamically.
- Apply to `77.js`, `84.js`, `602.js`, `1249.js`.

### 6. Limited Logging and Debugging

**Issue**: Logging is minimal, making it hard to diagnose issues in production.

**Correction**:

- Implement structured logging using `winston` (Node.js) or `logging` (Python) for all scripts.
- Log key events: navigation, extraction, Firestore updates, errors, and retries.
- Example for Node.js:

  ```javascript
  logger.info(`Navigated to Local ${localNumber} job listings page`);
  logger.error(`Failed to scrape job details: ${err.message}`);
  ```

- Store logs in a file (`scraper.log`) for debugging.
- Apply to all scripts.

## Script-Specific Issues and Corrections

### 1. `playwright111.py`

**Issues**:

- Uses placeholder URL (`https://www.example.com`) instead of Local 111’s URL.
- Lacks job-specific extraction logic and Firestore integration.

**Corrections**:

- Update the URL to `https://www.ibew111.org/dispatch`.
- Implement job extraction logic similar to `111.js`:

  ```python
  async def scrape_jobs(page):
      await page.waitForSelector('div.dispatch_cms-item')
      jobs = await page.evaluate('''
        () => {
          const jobElements = document.querySelectorAll('div.journeyman-lineman.dispatch-section div.dispatch_cms-item');
          return Array.from(jobElements).map(job => ({
            company: job.querySelector('.company .dispatch-paragraph')?.innerText.trim() || 'N/A',
            jobClass: job.querySelector('.class .dispatch-paragraph')?.innerText.trim() || 'N/A',
            location: job.querySelector('.locations .dispatch-paragraph p')?.innerText.trim() || 'N/A',
            wage: job.querySelector('.wage .dispatch-paragraph')?.innerText.trim() || 'N/A',
            startDate: job.querySelector('.startdate .dispatch-paragraph')?.innerText.trim() || 'N/A'
          }));
        }
      ''')
      return jobs
  ```

- Add Firestore integration using the `google-cloud-firestore` Python client (`pip install google-cloud-firestore`):

  ```python
  from google.cloud import firestore
  db = firestore.AsyncClient(project='journeyman-jobs')
  async def update_firestore(jobs):
      current_job_ids = set()
      for job in jobs:
          job_id = f"111-{job['jobClass']}-{job['company']}"
          current_job_ids.add(job_id)
          await db.collection('jobs').document(job_id).set({
              'jobId': job_id,
              'localNumber': '111',
              'employer': job['company'],
              'jobClass': job['jobClass'],
              'location': job['location'],
              'wage': job['wage'],
              'startDate': job['startDate'],
              'description': 'N/A',
              'timestamp': firestore.SERVER_TIMESTAMP
          })
  ```

- Apply retries and logging as described in general corrections.

### 2. `77.js`, `84.js`, `602.js`

**Issues**:

- Hardcoded job pairing assumes specific row indices, which is brittle.
- Incorrect `localNumber` (`'84'` used for all three scripts).
- No data validation for extracted fields.

**Corrections**:

- Fix `localNumber`:
  - `77.js`: Set `localNumber = '77'`.
  - `84.js`: Set `localNumber = '84'`.
  - `602.js`: Set `localNumber = '602'`.
- Replace hardcoded pairing with dynamic grouping (see general correction for hardcoded assumptions).
- Add data validation and sanitization before Firestore storage.
- Apply retries and logging as described.

### 3. `111.js`

**Issues**:

- Incorrect proxy protocol (`https://` instead of `wss://`).
- Limited error handling for classification scraping.
- No sanitization of extracted fields beyond `generateJobId`.

**Corrections**:

- Update `SBR_WS_ENDPOINT` to `wss://`.
- Add retry logic for each classification:

  ```javascript
  for (const classification of classifications) {
    await pRetry(async () => {
      const jobs = await scrapeJobsForClassification(page, classification);
      await updateDatabaseWithJobs(classification, jobs);
    }, { retries: 3 });
    await new Promise(resolve => setTimeout(resolve, 3000));
  }
  ```

- Sanitize extracted fields using `sanitizeJobData` (see general corrections).
- Apply logging as described.

### 4. `125.js` and `125_copy.js`

**Issues**:

- Limited field extraction (`title`, `link`, `description` only).
- Generic detail page scraping (`div.w3-card`) lacks structure.
- `125_copy.js` lacks Firestore integration.

**Corrections**:

- Expand extraction to include required fields:

  ```javascript
  const jobs = await page.evaluate(() => {
    const jobRows = Array.from(document.querySelectorAll('table tbody tr.resprow'));
    return jobRows.map(row => {
      const titleElement = row.querySelector('a');
      const descriptionElement = row.querySelector('div.w3-card');
      if (!titleElement || !descriptionElement) return null;
      const description = descriptionElement.textContent.trim();
      const employerMatch = description.match(/Employer:\s*([^\n]+)/i);
      const locationMatch = description.match(/Location:\s*([^\n]+)/i);
      const jobClassMatch = description.match(/Position:\s*([^\n]+)/i);
      return {
        title: titleElement.textContent.trim(),
        link: titleElement.href,
        employer: employerMatch ? employerMatch[1].trim() : 'N/A',
        location: locationMatch ? locationMatch[1].trim() : 'N/A',
        jobClass: jobClassMatch ? jobClassMatch[1].trim() : 'N/A',
        description
      };
    }).filter(job => job !== null);
  });
  ```

- Scrape structured data from detail pages:

  ```javascript
  for (const job of jobs) {
    await page.goto(job.link, { waitUntil: 'networkidle0' });
    const detailedData = await page.evaluate(() => {
      const detailCard = document.querySelector('div.w3-card');
      if (!detailCard) return {};
      const text = detailCard.textContent.trim();
      const wageMatch = text.match(/Wage:\s*\$?([\d.]+)/i);
      const startDateMatch = text.match(/Start Date:\s*([^\n]+)/i);
      return {
        wage: wageMatch ? wageMatch[1] : 'N/A',
        startDate: startDateMatch ? startDateMatch[1].trim() : 'N/A'
      };
    });
    Object.assign(job, detailedData);
    await page.goBack();
  }
  ```

- Add Firestore integration to `125_copy.js` using the same logic as `125.js`.
- Apply validation, retries, and logging as described.

### 5. `226.py`

**Issues**:

- No Firestore integration; saves to JSON files.
- Generic CSS selectors (`.job-listing`, `h2.title`) may not match the site.
- LLM dependency adds cost and latency.
- Limited error handling.

**Corrections**:

- Add Firestore integration:

  ```python
  from google.cloud import firestore
  db = firestore.AsyncClient(project='journeyman-jobs')
  async def update_firestore(jobs):
      current_job_ids = set()
      for job in jobs:
          job_id = f"226-{job['job_class']}-{job['company']}"
          current_job_ids.add(job_id)
          await db.collection('jobs').document(job_id).set({
              'jobId': job_id,
              'localNumber': '226',
              'employer': job['company'],
              'jobClass': job['job_class'],
              'location': job['location'],
              'wage': job['pay'],
              'startDate': 'N/A',
              'description': job['description'],
              'timestamp': firestore.SERVER_TIMESTAMP
          })
  ```

- Validate CSS selectors by inspecting `https://www.ibew226.com/ibew226_dir/Jobs` and updating the schema:

  ```python
  css_schema = {
      "name": "Job Listings",
      "baseSelector": ".job-posting", // Adjust based on actual DOM
      "fields": [
          {"name": "job_class", "selector": ".job-title", "type": "text"},
          {"name": "company", "selector": ".company-name", "type": "text"},
          {"name": "location", "selector": ".job-location", "type": "text"},
          {"name": "pay", "selector": ".job-pay", "type": "text"},
          {"name": "description", "selector": ".job-description", "type": "text"}
      ]
  }
  ```

- Remove LLM extraction unless critical for specific fields; if retained, add retry logic:

  ```python
  @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
  async def run_llm_extraction(crawler, url):
      return await crawler.arun(url=url, extraction_strategy=llm_strategy, bypass_cache=True)
  ```

- Apply validation, retries, and logging as described.

### 6. `1249.js`

**Issues**:

- Hardcoded `Journeyman_Lineman` classification.
- CSV parsing assumes fixed headers.
- Incorrect proxy protocol (`https://`).

**Corrections**:

- Extract classification from `Type of Work`:

  ```javascript
  const jobData = {
    ...job,
    localNumber,
    classification: job['Type of Work']?.trim() || 'Journeyman_Lineman',
    timestamp: firestoreJobsMap.has(jobId) ? firestoreJobsMap.get(jobId).timestamp : FieldValue.serverTimestamp()
  };
  ```

- Make CSV header mapping dynamic:

  ```javascript
  const headers = rows[0].map(h => h.trim().replace(/^["'\s]+|["'\s]+$/g, '')); // Handle quoted headers
  for (let i = 1; i < rows.length; i++) {
    const row = rows[i];
    if (!row[0] || row.length < 3) continue;
    const jobData = {};
    headers.forEach((header, index) => {
      jobData[header] = row[index] ? row[index].trim().replace(/^["'\s]+|["'\s]+$/g, '') : 'N/A';
    });
    filteredJobPostings.push(jobData);
  }
  ```

- Update `SBR_WS_ENDPOINT` to `wss://`.
- Apply validation, retries, and logging as described.

## Implementation Steps

1. **Install Dependencies**:
   - Node.js: `npm install p-retry winston @google-cloud/firestore puppeteer-core`.
   - Python: `pip install tenacity google-cloud-firestore playwright crawl4ai`.

2. **Apply General Corrections**:
   - Update proxy endpoints to `wss://`.
   - Add retry logic and logging to all scripts.
   - Implement data validation and sanitization.
   - Standardize Firestore schema.

3. **Apply Script-Specific Corrections**:
   - Update each script as outlined above.
   - Test changes locally to ensure extraction and Firestore updates work.

4. **Test the Scripts**:
   - Run each script against its target URL.
   - Verify Firestore data for completeness and consistency.
   - Check logs for errors or warnings.

5. **Deploy and Monitor**:
   - Schedule scripts to run daily using `node-cron` or a cloud scheduler.
   - Monitor logs and Firestore for anomalies (e.g., missing fields, duplicate jobs).
   - Regularly inspect target websites for DOM or URL changes.

## Testing Checklist

- [ ] Proxy connects successfully with `wss://`.
- [ ] All required fields are extracted and validated.
- [ ] Firestore documents match the standardized schema.
- [ ] Outdated jobs are deleted correctly.
- [ ] Logs capture navigation, extraction, and Firestore events.
- [ ] Scripts handle network errors and retries gracefully.

## Notes

- Coordinate with the development team to ensure Firestore credentials are secure and accessible.
- Document any site-specific quirks (e.g., dynamic content, authentication) for future maintenance.
- If issues persist (e.g., site structure changes), escalate to the senior developer for DOM inspection and selector updates.

This plan ensures the scraping scripts are robust, reliable, and aligned with the **Journeyman Jobs** app’s requirements for accurate and trustworthy job data.

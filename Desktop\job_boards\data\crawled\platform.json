[{"local_union": "1", "platform": ""}, {"local_union": "2", "platform": ""}, {"local_union": "3", "platform": ""}, {"local_union": "9", "platform": ""}, {"local_union": "12", "platform": "wordpress"}, {"local_union": "17", "platform": ""}, {"local_union": "37", "platform": ""}, {"local_union": "42", "platform": "unionactive"}, {"local_union": "44", "platform": "unionactive"}, {"local_union": "47", "platform": ""}, {"local_union": "53", "platform": ""}, {"local_union": "55", "platform": "SquareSpace"}, {"local_union": "57", "platform": "unionactive"}, {"local_union": "71", "platform": "wordpress"}, {"local_union": "77", "platform": "workingsystems"}, {"local_union": "84", "platform": "workingsystems"}, {"local_union": "89", "platform": "wordpress"}, {"local_union": "102", "platform": "workingsystems"}, {"local_union": "104", "platform": "workingsystems"}, {"local_union": "105", "platform": "unionactive"}, {"local_union": "111", "platform": ""}, {"local_union": "113", "platform": ""}, {"local_union": "115", "platform": "workingsystems"}, {"local_union": "120", "platform": ""}, {"local_union": "125", "platform": "unionactive"}, {"local_union": "126", "platform": ""}, {"local_union": "141", "platform": ""}, {"local_union": "145", "platform": "wordpress"}, {"local_union": "160", "platform": "workingsystems"}, {"local_union": "164", "platform": "workingsystems"}, {"local_union": "177", "platform": ""}, {"local_union": "193", "platform": "workingsystems"}, {"local_union": "196", "platform": "unionactive"}, {"local_union": "206", "platform": "unionactive"}, {"local_union": "220", "platform": "unionactive"}, {"local_union": "222", "platform": "unionactive"}, {"local_union": "238", "platform": ""}, {"local_union": "245", "platform": "unionactive"}, {"local_union": "252", "platform": ""}, {"local_union": "258", "platform": "unionactive"}, {"local_union": "278", "platform": "workingsystems"}, {"local_union": "291", "platform": "unionactive"}, {"local_union": "295", "platform": ""}, {"local_union": "301", "platform": ""}, {"local_union": "303", "platform": ""}, {"local_union": "304", "platform": "workingsystems"}, {"local_union": "307", "platform": ""}, {"local_union": "309", "platform": ""}, {"local_union": "317", "platform": "unionactive"}, {"local_union": "351", "platform": "workingsystems"}, {"local_union": "353", "platform": ""}, {"local_union": "379", "platform": "unionactive"}, {"local_union": "396", "platform": ""}, {"local_union": "400", "platform": ""}, {"local_union": "402", "platform": ""}, {"local_union": "424", "platform": ""}, {"local_union": "426", "platform": ""}, {"local_union": "429", "platform": ""}, {"local_union": "436", "platform": ""}, {"local_union": "443", "platform": "workingsystems"}, {"local_union": "449", "platform": "unionactive"}, {"local_union": "456", "platform": ""}, {"local_union": "459", "platform": ""}, {"local_union": "474", "platform": ""}, {"local_union": "483", "platform": "unionactive"}, {"local_union": "495", "platform": ""}, {"local_union": "530", "platform": "wordpress"}, {"local_union": "532", "platform": ""}, {"local_union": "553", "platform": "unionactive"}, {"local_union": "558", "platform": ""}, {"local_union": "568", "platform": ""}, {"local_union": "583", "platform": ""}, {"local_union": "586", "platform": ""}, {"local_union": "596", "platform": ""}, {"local_union": "602", "platform": "workingsystems"}, {"local_union": "611", "platform": ""}, {"local_union": "613", "platform": ""}, {"local_union": "649", "platform": "unionactive"}, {"local_union": "659", "platform": "unionactive"}, {"local_union": "666", "platform": "unionactive"}, {"local_union": "676", "platform": ""}, {"local_union": "681", "platform": ""}, {"local_union": "700", "platform": "unionworx"}, {"local_union": "702", "platform": ""}, {"local_union": "738", "platform": ""}, {"local_union": "760", "platform": ""}, {"local_union": "768", "platform": ""}, {"local_union": "769", "platform": ""}, {"local_union": "773", "platform": ""}, {"local_union": "776", "platform": ""}, {"local_union": "804", "platform": "workingsystems"}, {"local_union": "816", "platform": ""}, {"local_union": "840", "platform": ""}, {"local_union": "852", "platform": ""}, {"local_union": "876", "platform": ""}, {"local_union": "903", "platform": ""}, {"local_union": "934", "platform": ""}, {"local_union": "953", "platform": ""}, {"local_union": "968", "platform": ""}, {"local_union": "995", "platform": ""}, {"local_union": "1002", "platform": "laborpower"}, {"local_union": "1049", "platform": "NEP Services and Connect Plus+ App"}, {"local_union": "1186", "platform": ""}, {"local_union": "1245", "platform": "HyperArts"}, {"local_union": "1249", "platform": "workingsystems"}, {"local_union": "1250", "platform": ""}, {"local_union": "1316", "platform": ""}, {"local_union": "1319", "platform": ""}, {"local_union": "1340", "platform": "unionactive"}, {"local_union": "1393", "platform": ""}, {"local_union": "1426", "platform": "unionactive"}, {"local_union": "1516", "platform": ""}, {"local_union": "1525", "platform": ""}, {"local_union": "1531", "platform": ""}, {"local_union": "1547", "platform": ""}, {"local_union": "1579", "platform": ""}, {"local_union": "1620", "platform": ""}, {"local_union": "1687", "platform": ""}, {"local_union": "1701", "platform": ""}, {"local_union": "1925", "platform": ""}, {"local_union": "1928", "platform": ""}, {"local_union": "2067", "platform": ""}, {"local_union": "2085", "platform": ""}, {"local_union": "2150", "platform": "unionactive"}, {"local_union": "2286", "platform": ""}]
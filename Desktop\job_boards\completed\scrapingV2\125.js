const puppeteer = require('puppeteer-core');
const path = require('path');
const { Firestore, FieldValue } = require('@google-cloud/firestore');

// Set the environment variable for Google Cloud credentials
process.env.GOOGLE_APPLICATION_CREDENTIALS = path.resolve(__dirname, 'X:/Journeyman_Jobs/v3/core/storage/config/firebase_config.json');

// Proxy Configuration (if needed)
const SBR_WS_ENDPOINT = `wss://brd-customer-hl_482339a5-zone-scraping222_111:<EMAIL>:9222`;

// Firebase Configuration
const firebaseConfig = {
  projectId: 'journeyman-jobs',
  keyFilename: path.resolve(__dirname, 'X:/Journeyman_Jobs/v3/core/storage/config/firebase_config.json')
};

const db = new Firestore(firebaseConfig);

// Function to sanitize strings for Firestore document IDs
function sanitizeString(str) {
  return str.replace(/[\/\\\.\#\$\[\]]/g, '').replace(/\s+/g, '_');
}

// Function to generate a consistent job ID
function generateJobId(localNumber, position, company) {
  return `${localNumber}-${sanitizeString(position)}-${sanitizeString(company)}`;
}

async function main() {
  console.log('Starting script...');
  const browser = await puppeteer.connect({
    browserWSEndpoint: SBR_WS_ENDPOINT,
  });

  try {
    console.log('Connected! Navigating...');
    const page = await browser.newPage();
    await page.goto('https://www.ibew125.com/index.cfm?zone=/unionactive/private_view_page.cfm&page=Job20Listings', {
      waitUntil: 'networkidle0'
    });

    // Extract job data
    const jobs = await page.evaluate(() => {
      const jobRows = Array.from(document.querySelectorAll('table tbody tr.resprow'));

      return jobRows.map(row => {
        const titleElement = row.querySelector('a');
        const descriptionElement = row.querySelector('div.w3-card');

        if (titleElement && descriptionElement) {
          return {
            title: titleElement.textContent.trim(),
            link: titleElement.href,
            description: descriptionElement.textContent.trim()
          };
        }
        return null;
      }).filter(job => job !== null);
    });

    console.log('Scraped Job Details:', jobs);

    if (jobs.length === 0) {
      console.log('No jobs found. Double-check the page structure and selectors.');
    }

    // Extract local number (in this case, using '125' as an example)
    const localNumber = '125';

    // Fetch existing jobs from Firestore for the local number
    const firestoreJobsSnapshot = await db.collection('jobs').where('localNumber', '==', localNumber).get();
    const firestoreJobsMap = new Map();
    firestoreJobsSnapshot.forEach(doc => {
      firestoreJobsMap.set(doc.id, doc.data());
    });

    // Prepare a set to keep track of current job IDs
    const currentJobIds = new Set();

    // Process scraped jobs
    for (const job of jobs) {
      const jobId = generateJobId(localNumber, job.title, job.link);
      currentJobIds.add(jobId);

      const jobData = {
        ...job,
        localNumber,
        timestamp: firestoreJobsMap.has(jobId) ? firestoreJobsMap.get(jobId).timestamp : FieldValue.serverTimestamp()
      };

      const jobDocRef = db.collection('jobs').doc(jobId);

      if (firestoreJobsMap.has(jobId)) {
        // Update existing job without changing the initial timestamp
        await jobDocRef.update({
          ...jobData,
          timestamp: firestoreJobsMap.get(jobId).timestamp
        });
        console.log(`Updated job ${jobId}`);
      } else {
        // Create new job with initial timestamp
        await jobDocRef.set(jobData);
        console.log(`Added new job ${jobId}`);
      }

      // Navigate to the job listing link and scrape detailed information
      await page.goto(job.link, { waitUntil: 'networkidle0' });
      const detailedJobData = await page.evaluate(() => {
        // Extract detailed job information
        const detailedInfo = document.querySelector('div.w3-card');
        return detailedInfo ? detailedInfo.textContent.trim() : '';
      });

      console.log(`Scraped detailed information for job ${jobId}:`, detailedJobData);

      // Return to the main page
      await page.goBack();
    }

    // Delete jobs from Firestore that are no longer on the website
    for (const [jobId, jobData] of firestoreJobsMap.entries()) {
      if (!currentJobIds.has(jobId)) {
        await db.collection('jobs').doc(jobId).delete();
        console.log(`Deleted outdated job ${jobId}`);
      }
    }

  } catch (err) {
    console.error('An error occurred:', err);
  } finally {
    await browser.close();
  }
}

if (require.main === module) {
  main().catch(err => {
    console.error(err.stack || err);
    process.exit(1);
  });
}

module.exports = main;

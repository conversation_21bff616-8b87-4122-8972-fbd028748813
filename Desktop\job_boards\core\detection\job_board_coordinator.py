from typing import Dict, List, Optional, NamedTuple
from dataclasses import dataclass
import logging
from pathlib import Path
import json
import asyncio
import datetime

@dataclass
class DetectionResult:
    url: str
    is_job_board: bool
    confidence: float
    local_number: Optional[str]
    evidence: List[str]
    platform: Optional[str]
    verified: bool = False

class JobBoardCoordinator:
    """Coordinates multiple detection strategies to identify job boards"""
    
    def __init__(
        self,
        url_validator,
        ml_detector,
        content_analyzer,
        confidence_threshold: float = 0.8
    ):
        self.url_validator = url_validator
        self.ml_detector = ml_detector
        self.content_analyzer = content_analyzer
        self.confidence_threshold = confidence_threshold
        self.logger = logging.getLogger(__name__)
        
        # Track successful identifications
        self.verified_job_boards: Dict[str, DetectionResult] = {}
        
    async def identify_job_board(self, url: str, html_content: str) -> DetectionResult:
        """Run complete job board identification process"""
        evidence = []
        
        try:
            # URL Validation
            url_result = self.url_validator.validate_url(url)
            evidence.append(f"URL validation score: {url_result.score}")
            
            # ML-based detection
            ml_result = await self.ml_detector.detect_job_board(html_content, url)
            evidence.append(f"ML detection score: {ml_result.ml_score}")
            evidence.extend(f"ML feature: {f}" for f in ml_result.features_found)
            
            # Content analysis
            content_result = await self.content_analyzer.analyze_content(html_content)
            evidence.append(f"Content structure score: {content_result.structure_score}")
            evidence.append(f"Content relevance score: {content_result.content_score}")
            evidence.extend(content_result.job_indicators)
            
            # Combine scores with weights
            combined_confidence = (
                (url_result.score * 0.3) +  # URL structure
                (ml_result.confidence * 0.4) +  # ML prediction
                (content_result.structure_score * 0.15) +  # Page structure
                (content_result.content_score * 0.15)  # Content relevance
            )
            
            is_job_board = combined_confidence >= self.confidence_threshold
            
            # If we detect specific IBEW job content, boost confidence
            if content_result.classified_content['positions_found']:
                combined_confidence = min(1.0, combined_confidence + 0.1)
                evidence.append(
                    f"Found IBEW positions: {content_result.classified_content['positions_found']}"
                )
            
            result = DetectionResult(
                url=url,
                is_job_board=is_job_board,
                confidence=combined_confidence,
                local_number=url_result.local_number,
                evidence=evidence,
                platform=ml_result.platform or url_result.platform,
                verified=combined_confidence >= 0.9
            )
            
            # Store verified results
            if result.verified:
                self.verified_job_boards[url] = result
                # Add to validator's known job boards if verified
                if result.local_number:
                    self.url_validator.add_job_board_url(result.local_number, url)
                
            return result
            
        except Exception as e:
            self.logger.error(f"Error analyzing {url}: {str(e)}")
            return DetectionResult(
                url=url,
                is_job_board=False,
                confidence=0.0,
                local_number=None,
                evidence=[f"Error during analysis: {str(e)}"],
                platform=None,
                verified=False
            )
    
    def save_verified_boards(self, output_path: Path) -> None:
        """Save verified job boards to JSON"""
        output_data = {
            url: {
                'local_number': result.local_number,
                'confidence': result.confidence,
                'platform': result.platform,
                'evidence': result.evidence,
                'verification_date': datetime.now().isoformat()
            }
            for url, result in self.verified_job_boards.items()
        }
        
        with open(output_path, 'w') as f:
            json.dump(output_data, f, indent=2)
            
        self.logger.info(f"Saved {len(self.verified_job_boards)} verified job boards")